#!/usr/bin/env python3
"""
Test du service de validation backend
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configuration Django minimale
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myapp.settings')

import django
django.setup()

from backend.myapp.services.scenario_backend_validation_service import ScenarioBackendValidationService

def test_validation_service():
    """Test du service de validation"""
    print("=== Test du service de validation backend ===")
    
    service = ScenarioBackendValidationService()
    
    # Contenu du scénario de test
    scenario_content = """TRANSFERT {INIT 7x3; EDIT 1 [0,0]; EDIT 1 [0,2]; EDIT 1 [2,0]; EDIT 1 [2,2]; EDIT 5 [2,3]; EDIT 1 [2,5]}
AND ([0,0 2,2] [0,4 2,6])
REPLACE 1 2 [0,0 2,2]
EXTRACT [0,0 2,2]
END"""
    
    # Test avec une tâche fictive
    task_id = "0520fde7"
    subset = "training"
    test_index = 0
    
    print(f"Test de validation pour tâche {task_id}, subset {subset}, test {test_index}")
    print(f"Contenu du scénario:")
    print(scenario_content)
    
    # Exécuter la validation
    try:
        result = service.validate_scenario(subset, task_id, scenario_content, test_index)
        print(f"\nRésultat de validation:")
        print(f"Valid: {result.get('is_valid_by_backend', False)}")
        print(f"Error: {result.get('error_message', 'Aucune erreur')}")
        print(f"Command count: {result.get('command_count', 0)}")
        
        if result.get('execution_result'):
            exec_result = result['execution_result']
            print(f"Execution success: {exec_result.get('success', False)}")
            if exec_result.get('grid') is not None:
                print(f"Generated grid: {exec_result['grid']}")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_validation_service()