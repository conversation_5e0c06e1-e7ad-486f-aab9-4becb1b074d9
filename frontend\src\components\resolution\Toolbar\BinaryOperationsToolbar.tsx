// src/components/resolution/Toolbar/BinaryOperationsToolbar.tsx
import React from 'react';
import { useSelection } from '../hooks';
import { useAutomationStore } from '../hooks/useAutomationStore';
import { useGridStateStore } from '../hooks/useGridStateStore';
import { useNotification } from '../../../hooks/useNotification';
import styles from './Toolbar.module.css';

/**
 * NOUVELLE BARRE OPÉRATIONS BINAIRES
 * Contient les opérateurs logiques : XOR, AND, OR
 * Fonctionne en remplaçant SELECT par l'opérateur dans la dernière commande de sélection
 */
export const BinaryOperationsToolbar: React.FC = () => {
    // Hooks pour l'état de la sélection et de la grille
    const { selectedCells, rectangleCollection, selectRect, clearSelection } = useSelection();
    const { getCurrentGrid, replaceCurrentGrid } = useGridStateStore();
    const { showError } = useNotification();



    // Vérifier s'il y a une sélection
    const hasSelection = selectedCells.size > 0;

    // Vérifier le nombre de rectangles de sélection disponibles
    const rectangleCount = rectangleCollection.rectangles.size;

    // Vérifier le nombre de commandes SELECT disponibles
    const automationStore = useAutomationStore();
    const currentCommands = automationStore.getCurrentCommands();
    const selectCommandsCount = currentCommands.filter(cmd => cmd.trim().startsWith('SELECT ')).length;



    // Fonction pour valider qu'une zone a des valeurs homogènes (toutes les cellules non-zéro ont la même valeur)
    const validateZoneHomogeneity = (rectangle: any, grid: any): { isValid: boolean; value: number } => {
        let nonZeroValue: number | null = null;

        for (let row = rectangle.startRow; row <= rectangle.endRow; row++) {
            for (let col = rectangle.startCol; col <= rectangle.endCol; col++) {
                const cellValue = grid.getCell(row, col);
                if (cellValue !== 0) {
                    if (nonZeroValue === null) {
                        nonZeroValue = cellValue;
                    } else if (nonZeroValue !== cellValue) {
                        return { isValid: false, value: 0 };
                    }
                }
            }
        }

        return { isValid: true, value: nonZeroValue || 0 };
    };

    // Fonction pour valider que toutes les zones ont la même taille
    const validateZonesSameSize = (rectangles: any[]): boolean => {
        if (rectangles.length < 2) return true;

        const firstRect = rectangles[0];
        const firstWidth = firstRect.endCol - firstRect.startCol + 1;
        const firstHeight = firstRect.endRow - firstRect.startRow + 1;

        for (let i = 1; i < rectangles.length; i++) {
            const rect = rectangles[i];
            const width = rect.endCol - rect.startCol + 1;
            const height = rect.endRow - rect.startRow + 1;

            if (width !== firstWidth || height !== firstHeight) {
                return false;
            }
        }

        return true;
    };

    // Fonction pour appliquer l'opération binaire sur les valeurs
    const applyBinaryOperation = (operator: string, value1: number, value2: number): number => {
        // Convertir en binaire : 0 = 0, 1-9 = 1
        const bin1 = value1 === 0 ? 0 : 1;
        const bin2 = value2 === 0 ? 0 : 1;

        let resultBin: number;
        switch (operator) {
            case 'XOR':
                resultBin = bin1 ^ bin2;
                break;
            case 'AND':
                resultBin = bin1 & bin2;
                break;
            case 'OR':
                resultBin = bin1 | bin2;
                break;
            default:
                return 0;
        }

        // Convertir le résultat binaire en valeur
        if (resultBin === 0) {
            return 0; // Fond
        } else {
            // Garder la valeur non-zéro (priorité à value1, puis value2)
            return value1 !== 0 ? value1 : value2;
        }
    };

    // Fonction pour appliquer un opérateur binaire en remplaçant SELECT
    const applyBinaryOperatorToSelection = (operator: string) => {
        const grid = getCurrentGrid();
        if (!grid || !hasSelection) return;

        const automationStore = useAutomationStore.getState();
        const currentCommands = automationStore.getCurrentCommands();

        // Vérifier si l'enregistrement est actif
        if (!automationStore.isRecording) {
            showError(`Enregistrement non actif pour l'opérateur ${operator}`);
            console.warn(`[BinaryOperationsToolbar] Enregistrement non actif pour l'opérateur ${operator}.`);
            return;
        }

        // Obtenir les rectangles de sélection
        const rectangles = Array.from(rectangleCollection.rectangles.values());
        console.log(`[BinaryOperationsToolbar] ${operator} - Nombre de rectangles: ${rectangles.length}`);

        // Validation 1: Vérifier le nombre de rectangles
        if (operator === 'XOR' && rectangles.length !== 2) {
            showError(`XOR nécessite exactement 2 sélections (trouvées: ${rectangles.length})`);
            console.warn(`[BinaryOperationsToolbar] XOR nécessite exactement 2 sélections, trouvées: ${rectangles.length}`);
            return;
        }
        if ((operator === 'AND' || operator === 'OR') && rectangles.length < 2) {
            showError(`${operator} nécessite au moins 2 sélections (trouvées: ${rectangles.length})`);
            console.warn(`[BinaryOperationsToolbar] ${operator} nécessite au moins 2 sélections, trouvées: ${rectangles.length}`);
            return;
        }

        // Validation 2: Vérifier que toutes les zones ont la même taille
        if (!validateZonesSameSize(rectangles)) {
            showError(`${operator}: Toutes les zones doivent avoir la même taille`);
            console.warn(`[BinaryOperationsToolbar] ${operator} - Toutes les zones doivent avoir la même taille`);
            return;
        }

        // Validation 3: Vérifier l'homogénéité des valeurs dans chaque zone
        const zoneValues: number[] = [];
        for (let i = 0; i < rectangles.length; i++) {
            const validation = validateZoneHomogeneity(rectangles[i], grid);
            if (!validation.isValid) {
                showError(`${operator}: Zone ${i + 1} contient des valeurs non-homogènes`);
                console.warn(`[BinaryOperationsToolbar] ${operator} - Zone ${i} contient des valeurs non-homogènes`);
                return;
            }
            zoneValues.push(validation.value);
            console.log(`[BinaryOperationsToolbar] ${operator} - Zone ${i}: valeur homogène = ${validation.value}`);
        }

        // Appliquer l'opération binaire cellule par cellule sur la première zone
        const newGrid = grid.clone();
        const firstRect = rectangles[0];
        const width = firstRect.endCol - firstRect.startCol + 1;
        const height = firstRect.endRow - firstRect.startRow + 1;

        console.log(`[BinaryOperationsToolbar] ${operator} - Traitement zone ${width}x${height}`);

        for (let row = 0; row < height; row++) {
            for (let col = 0; col < width; col++) {
                const firstZoneRow = firstRect.startRow + row;
                const firstZoneCol = firstRect.startCol + col;

                // Obtenir la valeur de la première zone
                let resultValue = grid.getCell(firstZoneRow, firstZoneCol) ?? 0;
                console.log(`[BinaryOperationsToolbar] ${operator} - Cellule [${firstZoneRow},${firstZoneCol}] - Valeur initiale: ${resultValue}`);

                // Appliquer l'opération avec toutes les autres zones
                for (let i = 1; i < rectangles.length; i++) {
                    const otherRect = rectangles[i];
                    const otherZoneRow = otherRect.startRow + row;
                    const otherZoneCol = otherRect.startCol + col;
                    const otherValue = grid.getCell(otherZoneRow, otherZoneCol) ?? 0;

                    const previousValue = resultValue;
                    resultValue = applyBinaryOperation(operator, resultValue, otherValue);
                    console.log(`[BinaryOperationsToolbar] ${operator} - Cellule [${firstZoneRow},${firstZoneCol}] - ${previousValue} ${operator} ${otherValue} = ${resultValue}`);
                }

                // Écrire le résultat dans la première zone
                newGrid.setCell(firstZoneRow, firstZoneCol, resultValue);
            }
        }

        // Mettre à jour la grille
        replaceCurrentGrid(newGrid);

        // Mettre à jour la sélection pour ne garder que la première zone
        clearSelection();
        selectRect(firstRect.startRow, firstRect.startCol, firstRect.endRow, firstRect.endCol, 'replace');

        // Chercher la dernière commande SELECT qui contient toutes les sélections groupées
        const lastSelectIndex = [...currentCommands].reverse().findIndex(cmd => cmd.trim().startsWith('SELECT '));

        if (lastSelectIndex === -1) {
            console.warn(`[BinaryOperationsToolbar] Aucune commande SELECT trouvée pour l'opérateur ${operator}.`);
            return;
        }

        // Index réel dans le tableau (car reverse)
        const selectIndex = currentCommands.length - 1 - lastSelectIndex;
        const selectCommand = currentCommands[selectIndex];

        // Extraire les coordonnées de la commande SELECT
        const coordsMatch = selectCommand.match(/^SELECT\s+(.+)$/);
        if (!coordsMatch) {
            console.warn(`[BinaryOperationsToolbar] Format de commande SELECT invalide: ${selectCommand}`);
            return;
        }

        const coordinates = coordsMatch[1];

        // Créer la nouvelle commande avec l'opérateur binaire
        const newCommand = `${operator} ${coordinates}`;

        // Remplacer la commande SELECT par la nouvelle commande d'opérateur
        const updatedCommands = [
            ...currentCommands.slice(0, selectIndex),
            newCommand,
            ...currentCommands.slice(selectIndex + 1)
        ];

        automationStore.setCommands(updatedCommands);
        console.log(`[BinaryOperationsToolbar] Opérateur ${operator} appliqué: ${newCommand}`);
    };

    // Gestionnaires pour chaque opérateur
    const handleXorOperator = () => {
        applyBinaryOperatorToSelection('XOR');
    };

    const handleAndOperator = () => {
        applyBinaryOperatorToSelection('AND');
    };

    const handleOrOperator = () => {
        applyBinaryOperatorToSelection('OR');
    };

    return (
        <div className={styles.toolbarContainer}>
            <span className={styles.toolbarLabel}>Binaires :</span>

            <button
                className={styles.toolbarButton}
                onClick={handleXorOperator}
                disabled={!hasSelection || rectangleCount !== 2}
                title="XOR: Différence symétrique entre exactement 2 sélections"
            >
                ⊕ XOR
            </button>

            <button
                className={styles.toolbarButton}
                onClick={handleAndOperator}
                disabled={!hasSelection || rectangleCount < 2}
                title="AND: Intersection de 2 sélections ou plus"
            >
                ∩ AND
            </button>

            <button
                className={styles.toolbarButton}
                onClick={handleOrOperator}
                disabled={!hasSelection || rectangleCount < 2}
                title="OR: Union de 2 sélections ou plus"
            >
                ∪ OR
            </button>
        </div>
    );
};