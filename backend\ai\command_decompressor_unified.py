"""
Module de décompression des commandes unifiées.

Ce module décompresse UNIQUEMENT les commandes unifiées valides :
- Groupes de commandes (EDITS, FILLS, FLOODFILLS, REPLACES, CLEARS, 
  SURROUNDS, FLIPS, ROTATES, INSERTS, DELETES)
- Blocs de transfert (TRANSFERT)
- Blocs de motif (MOTIF)
- Commandes individuelles spéciales (INVERT, COLOR)

REJETTE EXPLICITEMENT :
- Toutes les commandes obsolètes (PROPOSE, VALIDATE, etc.)
- Tous les groupements interdits (RESIZES, INITS, EXTRACTS, SELECTS, PASTES, MULTIPLYS, DIVIDES etc.)
- Toutes les conversions legacy
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class CommandDecompressor:
    """
    Décompresse UNIQUEMENT les commandes unifiées valides vers format individuel.
    
    Groupes valides supportés :
    - EDITS {...} : Groupement d'EDIT
    - FILLS {...} : Groupement de FILL
    - FLOODFILLS {...} : Groupement de FLOODFILL
    - REPLACES {...} : Groupement de REPLACE
    - CLEARS {...} : Groupement de CLEAR
    - SURROUNDS {...} : Groupement de SURROUND
    - FLIPS(...) : Groupement de FLIP
    - ROTATES {...} : Groupement de ROTATE
    - INSERTS(...) : Groupement d'INSERT
    - DELETES(...) : Groupement de DELETE
    - TRANSFERT{...} : Bloc de transfert/initialisation
    - MOTIF{...} : Bloc de motif CUT\COPY\PASTE

    Commandes de modifications de sélection :
    - (INVERT(...)) : Inversion de sélection
    - (COLOR(...)): Sélection par couleur
    
    REJETTE EXPLICITEMENT tous les types obsolètes/interdits.
    """
    
    # Regex pour matcher les blocs unifiés (parenthèses ET accolades, avec espace optionnel)
    OPTIMIZED_BLOCK_REGEX = re.compile(r'^([A-Z_]+)\s*[\(\{](.*?)[\)\}]$')
    
    def __init__(self):
        """Initialise le décompresseur des commandes unifiées."""
        self.stats = {
            'total_processed': 0,
            'blocks_decompressed': 0,
            'commands_expanded': 0,
            'commands_rejected': 0
        }
    
    def decompress_commands(self, optimized_commands: List[str]) -> List[str]:
        """
        Décompresse une liste de commandes unifiées en commandes individuelles.
        
        Args:
            optimized_commands: Liste des commandes unifiées valides
            
        Returns:
            Liste des commandes individuelles décompressées
            
        Raises:
            ValueError: Si une commande obsolète/interdite est détectée
            
        Examples:
            ['EDITS {EDIT 7 [0,0]; EDIT 7 [0,1]}', 'TRANSFERT{INIT 3x3; EDIT 7 [0,0]}'] 
            -> ['EDIT 7 [0,0]', 'EDIT 7 [0,1]', 'INIT 3x3', 'EDIT 7 [0,0]']
        """
        self.stats['total_processed'] += len(optimized_commands)
        expanded_commands = []
        
        for command in optimized_commands:
            if not command or not command.strip():
                continue
                
            command = command.strip()
              # Vérifier si c'est un bloc optimisé
            block_info = self._parse_optimized_block(command)
            
            if block_info['is_block']:
                # Décompresser le bloc
                decompressed = self._decompress_block(
                    block_info['type'], 
                    block_info['content']
                )
                expanded_commands.extend(decompressed)
                self.stats['blocks_decompressed'] += 1
                self.stats['commands_expanded'] += len(decompressed)
                
                logger.debug(f"Decompressed {block_info['type']} block into {len(decompressed)} commands")
            else:
                # Commande simple au format unifié, la valider et l'ajouter
                if self._is_valid_individual_command(command):
                    expanded_commands.append(command)
                    logger.debug(f"Added individual command: {command}")
                else:
                    self.stats['commands_rejected'] += 1
                    logger.warning(f"Invalid individual command rejected: {command}")
                    # Ne pas lever d'exception pour les commandes individuelles invalides,                    # laisse le validateur final s'en charger
        
        logger.info(f"Decompression stats: {self.stats}")
        return expanded_commands

    def _parse_optimized_block(self, command: str) -> Dict[str, Any]:
        """
        Parse une commande pour détecter si c'est un bloc optimisé.
        
        Args:
            command: Commande à analyser
            
        Returns:
            Dictionnaire avec les clés :
            - is_block: bool, True si c'est un bloc optimisé
            - type: str, type du bloc (EDITS, TRANSFERT, etc.)
            - content: str, contenu du bloc
        """
        logger.debug(f"🔍 Parsing command: {command}")
        logger.debug(f"🔍 Using regex: {self.OPTIMIZED_BLOCK_REGEX.pattern}")
        
        match = self.OPTIMIZED_BLOCK_REGEX.match(command)
        logger.debug(f"🔍 Regex match result: {match}")
        
        if match:
            block_type = match.group(1)
            content = match.group(2)
            
            # Liste des vrais types de blocs groupés
            valid_block_types = {
                'EDITS', 'FILLS', 'FLOODFILLS', 'REPLACES', 'CLEARS',
                'SURROUNDS', 'FLIPS', 'ROTATES', 'INSERTS', 'DELETES', 
                'TRANSFERT', 'MOTIF'
            }
            
            # Vérifier si c'est vraiment un type de bloc valide
            if block_type in valid_block_types:
                logger.debug(f"✅ Valid block detected - Type: {block_type}, Content: {content}")
                return {
                    'is_block': True,
                    'type': block_type,
                    'content': content
                }
            else:
                logger.debug(f"❌ Not a valid block type: {block_type}, treating as individual command")
        
        logger.debug(f"❌ No block detected, treating as simple command")
        return {
            'is_block': False,
            'type': None,
            'content': None
        }
    
    def _decompress_block(self, block_type: str, content: str) -> List[str]:
        """
        Décompresse UNIQUEMENT les blocs de commandes unifiées valides.
        Rejette explicitement tout type non-autorisé.
        
        Args:
            block_type: Type du bloc (EDITS, TRANSFERT, etc.)
            content: Contenu du bloc
            
        Returns:
            Liste des commandes décompressées
            
        Raises:
            ValueError: Pour les types obsolètes ou interdits
        """
        try:
            # GROUPES VALIDES UNIQUEMENT
            if block_type == 'EDITS':
                return self._decompress_edits_block(content)
            elif block_type == 'FILLS':
                return self._decompress_fills_block(content)
            elif block_type == 'FLOODFILLS':
                return self._decompress_floodfills_block(content)
            elif block_type == 'REPLACES':
                return self._decompress_replaces_block(content)
            elif block_type == 'CLEARS':
                return self._decompress_clears_block(content)
            elif block_type == 'SURROUNDS':
                return self._decompress_surrounds_block(content)
            elif block_type == 'FLIPS':
                return self._decompress_flips_block(content)
            elif block_type == 'ROTATES':
                return self._decompress_rotates_block(content)
            elif block_type == 'INSERTS':
                return self._decompress_inserts_block(content)
            elif block_type == 'DELETES':
                return self._decompress_deletes_block(content)
            elif block_type == 'TRANSFERT':
                return self._decompress_transfert_block(content)
            elif block_type == 'MOTIF':
                return self._decompress_motif_block(content)
            
            # COMMANDES INDIVIDUELLES SPÉCIALES
            elif block_type == 'INVERT':
                return self._decompress_select_invert_block(content)
            elif block_type == 'COLOR':
                return self._decompress_select_color_block(content)
            
            # REJET EXPLICITE DE TOUT LE RESTE
            else:
                # Liste exhaustive des types interdits pour debugging
                forbidden_types = [
                   'PROPOSE', 'VALIDATE', 'DELETE_ROWS', 'DELETE_COLS', 
                    'INSERT_ROW', 'INSERT_COL', 'RESIZES', 'INITS',
                    'SELECT_INVERTS', 'SELECT_RELEASES', 'SELECT_COLORS',
                    'SPLIT', 'SPLITS', 'SELECTIONS', 'SURROUND_SELECTIONS',
                    'EXTRACTS', 'FLIP', 'SELECT_INVERT', 'SELECT_COLOR',
                    'SELECTS'
                ]
                
                if block_type in forbidden_types:
                    self.stats['commands_rejected'] += 1
                    logger.error(f"REJETÉ - Type de bloc obsolète/interdit : {block_type}")
                    raise ValueError(f"Type de commande obsolète non supporté : {block_type}")
                else:
                    self.stats['commands_rejected'] += 1
                    logger.error(f"REJETÉ - Type de bloc inconnu : {block_type}")
                    raise ValueError(f"Type de commande non reconnu : {block_type}")
                
        except Exception as e:
            logger.error(f"Erreur lors de la décompression du bloc {block_type}: {e}")
            raise

    def _decompress_edits_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc EDITS.
        
        Args:
            content: Contenu du bloc EDITS
            
        Returns:
            Liste des commandes EDIT individuelles
            
        Example:
            "EDIT 7 [0,0]; EDIT 7 [0,1]; EDIT 7 [0,2]"
            -> ["EDIT 7 [0,0]", "EDIT 7 [0,1]", "EDIT 7 [0,2]"]
        """
        if not content:
            return []
        
        # Séparer par ; et nettoyer
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        # Vérifier que toutes les commandes sont bien des EDIT
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('EDIT '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande EDIT invalide dans le bloc: {cmd}")
        
        return valid_commands

    def _decompress_transfert_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc TRANSFERT (format unifié avec accolades).
        
        Args:
            content: Contenu du bloc TRANSFERT
            
        Returns:
            Liste des commandes individuelles
            
        Example:
            "INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,1]"
            -> ["INIT 3x3", "EDIT 7 [0,0]", "EDIT 7 [0,1]"]
        """
        if not content:
            return []
        
        # Séparer par ; et nettoyer
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        # Valider les commandes (format unifié uniquement)
        valid_commands = []
        for cmd in commands:
            # Accepter les commandes du format unifié uniquement
            if (cmd.startswith(('INIT ', 'EDIT ', 'FILL ', 'REPLACE ', 'CLEAR ', 
                               'SURROUND ', 'EXTRACT ', 'FLIP ', 'ROTATE ', 
                               'INSERT ', 'DELETE ', 'COPY ', 
                               'CUT ', 'PASTE ', 'FLOODFILL ',
                               'MULTIPLY ', 'DIVIDE '))):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande non-unifié rejetée dans TRANSFERT: {cmd}")
        
        return valid_commands

    def _decompress_motif_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc MOTIF (format unifié avec accolades).
        
        Args:
            content: Contenu du bloc MOTIF
            
        Returns:
            Liste des commandes individuelles
            
        Example:
            MOTIF {CUT (COLOR 5,2,4,1 [8,7 14,16]); ROTATE RIGHT [9,10 11,13]; FLIP HORIZONTAL [9,10 12,12]; PASTE [12,2]}
            MOTIF {CUT (COLOR 5,1,4,2 [0,2 9,12]); ROTATE LEFT [3,5 8,8]; PASTE [16,9]}
        """
        if not content:
            return []
        
        # Séparer par ; et nettoyer
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        # Valider les commandes (format unifié uniquement)
        valid_commands = []
        for cmd in commands:
            # Accepter les commandes du format unifié uniquement
            if (cmd.startswith(('FLIP ', 'ROTATE ', 'MULTIPLY ', 'DIVIDE ',
                               'COPY ',  'CUT ', 'PASTE '))):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande non-unifié rejetée dans MOTIF: {cmd}")
        
        return valid_commands

    def _decompress_select_invert_block(self, content: str) -> List[str]:
        """Handle individual SELECT_INVERT command (not a group)"""
        if not content:
            return []
        return [f"SELECT_INVERT({content})"]
    
    def _decompress_select_color_block(self, content: str) -> List[str]:
        """Handle individual SELECT_COLOR command (not a group)"""
        if not content:
            return []
        return [f"SELECT_COLOR({content})"]

    def get_stats(self) -> Dict[str, int]:
        """
        Retourne les statistiques de décompression.
        
        Returns:
            Dictionnaire avec les statistiques
        """
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """Remet à zéro les statistiques."""
        self.stats = {
            'total_processed': 0,
            'blocks_decompressed': 0,
            'commands_expanded': 0,
            'commands_rejected': 0
        }

    def _decompress_fills_block(self, content: str) -> List[str]:
        """Décompresse un bloc FILLS."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('FILL '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande FILL invalide dans le bloc: {cmd}")
        
        return valid_commands
    
    def _decompress_floodfills_block(self, content: str) -> List[str]:
        """Décompresse un bloc FLOODFILLS."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('FLOODFILL '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande FLOODFILL invalide dans le bloc: {cmd}")
        
        return valid_commands
    
    def _decompress_replaces_block(self, content: str) -> List[str]:
        """Décompresse un bloc REPLACES."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('REPLACE '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande REPLACE invalide dans le bloc: {cmd}")
        
        return valid_commands
    
    def _decompress_flips_block(self, content: str) -> List[str]:
        """Décompresse un bloc FLIPS."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('FLIP '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande FLIP invalide dans le bloc: {cmd}")
        
        return valid_commands
    
    def _decompress_rotates_block(self, content: str) -> List[str]:
        """Décompresse un bloc ROTATES."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('ROTATE '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande ROTATE invalide dans le bloc: {cmd}")
        
        return valid_commands
    
    def _decompress_inserts_block(self, content: str) -> List[str]:
        """Décompresse un bloc INSERTS."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('INSERT '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande INSERT invalide dans le bloc: {cmd}")
        
        return valid_commands
    
    def _decompress_deletes_block(self, content: str) -> List[str]:
        """Décompresse un bloc DELETES."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('DELETE '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande DELETE invalide dans le bloc: {cmd}")
        
        return valid_commands
    
    def _decompress_clears_block(self, content: str) -> List[str]:
        """Décompresse un bloc CLEARS."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('CLEAR '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande CLEAR invalide dans le bloc: {cmd}")
        
        return valid_commands

    def _decompress_surrounds_block(self, content: str) -> List[str]:
        """Décompresse un bloc SURROUNDS."""
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('SURROUND '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Commande SURROUND invalide dans le bloc: {cmd}")
        
        return valid_commands

    def _is_valid_individual_command(self, command: str) -> bool:
        """
        Valide si une commande individuelle est dans un format accepté.
        
        Args:
            command: Commande individuelle à valider
            
        Returns:
            True si la commande est valide, False sinon
        """
        if not command or not command.strip():
            return False
            
        command = command.strip()
        
        # Liste des commandes individuelles valides au format unifié
        valid_individual_commands = [
            'INIT', 'EDIT', 'FILL', 'FLOODFILL', 'REPLACE', 'CLEAR',
            'SURROUND', 'EXTRACT', 'FLIP', 'ROTATE', 'INSERT', 'DELETE',
            'SELECT_RELEASE', 'COPY', 'CUT', 'PASTE', 'RESIZE', 'END',
            'MULTIPLY', 'DIVIDE',  # Nouvelles commandes ajoutées
            'AND', 'OR', 'XOR'  # Opérateurs binaires
        ]
        
        # Extraire le type de commande (premier mot)
        command_parts = command.split()
        if not command_parts:
            return False
            
        command_type = command_parts[0].upper()
        
        # Vérifier si c'est un type de commande valide
        if command_type in valid_individual_commands:
            logger.debug(f"✅ Commande individuelle valide: {command_type}")
            return True
        
        # Commandes spéciales avec paramètres entre parenthèses
        if command_type in ['(INVERT', '(COLOR'] and '(' in command:
            logger.debug(f"✅ Commande spéciale valide: {command_type}")
            return True
            
        logger.debug(f"❌ Commande individuelle non reconnue: {command_type}")
        return False

    def get_stats(self) -> Dict[str, int]:
        """
        Retourne les statistiques de décompression.
        
        Returns:
            Dictionnaire avec les statistiques
        """
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """Remet à zéro les statistiques."""
        self.stats = {
            'total_processed': 0,
            'blocks_decompressed': 0,
            'commands_expanded': 0,
            'commands_rejected': 0
        }


def decompress_scenario_commands(scenario_content: str) -> List[str]:
    """
    Fonction utilitaire pour décompresser le contenu d'un scénario.
    
    Args:
        scenario_content: Contenu du scénario (lignes séparées par \n)
        
    Returns:
        Liste des commandes décompressées
    """
    if not scenario_content:
        return []
    
    # Séparer par lignes et nettoyer
    lines = [line.strip() for line in scenario_content.split('\n') if line.strip()]
    
    # Décompresser
    decompressor = CommandDecompressor()
    return decompressor.decompress_commands(lines)
