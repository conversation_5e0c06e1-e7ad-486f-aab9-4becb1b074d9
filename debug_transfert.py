#!/usr/bin/env python3
"""
Debug du TRANSFERT pour comprendre la grille initiale
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.command_executor import CommandExecutor
from backend.command_system.unified_command import UnifiedCommand

def debug_transfert():
    """Debug du TRANSFERT"""
    print("=== Debug du TRANSFERT ===")
    
    executor = CommandExecutor()
    
    # Initialiser une grille 3x7
    init_cmd = UnifiedCommand.parse("INIT 3x7")
    if not executor._execute_unified_command(init_cmd):
        print("❌ Échec de l'initialisation")
        return
    
    print("Grille après INIT 3x7:")
    print(f"Shape: {executor.grid.shape}")
    print(executor.grid)
    
    # Créer un motif initial avec TRANSFERT
    transfert_cmd = UnifiedCommand.parse("TRANSFERT {INIT 3x7; EDIT 1 [0,0]; EDIT 1 [0,2]; EDIT 1 [2,0]; EDIT 1 [2,2]; EDIT 5 [2,3]; EDIT 1 [2,5]}")
    if not executor._execute_unified_command(transfert_cmd):
        print("❌ Échec du TRANSFERT")
        return
    
    print("Grille après TRANSFERT:")
    print(f"Shape: {executor.grid.shape}")
    print(executor.grid)
    
    # Analyser les zones
    print("\nAnalyse des zones:")
    print("Zone [0,0 2,2] (première zone):")
    for row in range(3):
        for col in range(3):
            if row < executor.grid.shape[0] and col < executor.grid.shape[1]:
                print(f"  [{row},{col}] = {executor.grid[row, col]}")
    
    print("Zone [0,4 2,6] (deuxième zone):")
    for row in range(3):
        for col in range(4, 7):
            if row < executor.grid.shape[0] and col < executor.grid.shape[1]:
                print(f"  [{row},{col}] = {executor.grid[row, col]}")

if __name__ == "__main__":
    debug_transfert()