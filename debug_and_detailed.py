#!/usr/bin/env python3
"""
Debug détaillé de l'opérateur AND
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.command_executor import CommandExecutor
from backend.command_system.unified_command import UnifiedCommand

def debug_and_detailed():
    """Debug détaillé de l'opérateur AND"""
    print("=== Debug détaillé de l'opérateur AND ===")
    
    executor = CommandExecutor()
    
    # Initialiser et créer le motif
    init_cmd = UnifiedCommand.parse("INIT 7x3")
    executor._execute_unified_command(init_cmd)
    
    transfert_cmd = UnifiedCommand.parse("TRANSFERT {INIT 7x3; EDIT 1 [0,0]; EDIT 1 [0,2]; EDIT 1 [2,0]; EDIT 1 [2,2]; EDIT 5 [2,3]; EDIT 1 [2,5]}")
    executor._execute_unified_command(transfert_cmd)
    
    print("Grille après TRANSFERT:")
    print(executor.grid)
    
    # Analyser les zones en détail
    print("\nAnalyse détaillée des zones:")
    
    # Zone 1: [0,0 2,2]
    print("Zone 1 [0,0 2,2]:")
    zone1_values = []
    for row in range(3):
        row_values = []
        for col in range(3):
            value = executor.grid[row, col]
            row_values.append(value)
            print(f"  [{row},{col}] = {value}")
        zone1_values.append(row_values)
    
    # Zone 2: [0,4 2,6]
    print("Zone 2 [0,4 2,6]:")
    zone2_values = []
    for row in range(3):
        row_values = []
        for col in range(4, 7):
            value = executor.grid[row, col]
            row_values.append(value)
            print(f"  [{row},{col}] = {value}")
        zone2_values.append(row_values)
    
    # Calculer le résultat attendu de l'AND
    print("\nCalcul AND cellule par cellule:")
    expected_zone1 = []
    for row in range(3):
        row_result = []
        for col in range(3):
            val1 = zone1_values[row][col]
            val2 = zone2_values[row][col]
            # AND: garder val1 si val1 != 0 ET val2 != 0, sinon 0
            result = val1 if (val1 != 0 and val2 != 0) else 0
            row_result.append(result)
            print(f"  [{row},{col}]: {val1} AND {val2} = {result}")
        expected_zone1.append(row_result)
    
    print(f"\nRésultat AND attendu pour zone 1: {expected_zone1}")
    
    # Exécuter l'AND
    and_cmd = UnifiedCommand.parse("AND ([0,0 2,2] [0,4 2,6])")
    executor._execute_unified_command(and_cmd)
    
    print("Grille après AND:")
    print(executor.grid)
    
    # Vérifier la zone 1 après AND
    print("\nZone 1 après AND:")
    actual_zone1 = []
    for row in range(3):
        row_values = []
        for col in range(3):
            value = executor.grid[row, col]
            row_values.append(value)
            print(f"  [{row},{col}] = {value}")
        actual_zone1.append(row_values)
    
    print(f"\nComparaison:")
    print(f"Attendu: {expected_zone1}")
    print(f"Obtenu:  {actual_zone1}")
    print(f"Match: {expected_zone1 == actual_zone1}")

if __name__ == "__main__":
    debug_and_detailed()