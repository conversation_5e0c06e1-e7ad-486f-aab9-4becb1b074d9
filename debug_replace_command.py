#!/usr/bin/env python3
"""
Script de débogage pour analyser le problème de la commande REPLACE
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.command_executor import CommandExecutor
import numpy as np

def test_replace_sequence():
    """Test de la séquence complète qui pose problème"""
    
    commands = [
        'TRANSFERT {INIT 7x3; EDIT 1 [0,0]; EDIT 1 [0,2]; EDIT 1 [2,0]; EDIT 1 [2,2]; EDIT 5 [2,3]; EDIT 1 [2,5]}',
        'AND ([0,0 2,2] [0,4 2,6])',
        'REPLACE 1 2 [0,0 2,2]',
        'EXTRACT [0,0 2,2]',
        'END'
    ]
    
    executor = CommandExecutor()
    
    print("=== TEST DE LA SÉQUENCE COMPLÈTE ===")
    
    for i, command in enumerate(commands):
        print(f"\n--- Étape {i+1}: {command} ---")
        
        # Exécuter la commande
        success = executor._execute_command(command)
        
        if not success:
            print(f"❌ Échec: {executor.error}")
            break
        else:
            print("✅ Succès")
        
        # Afficher l'état de la grille
        if executor.grid is not None:
            print(f"Grille actuelle ({executor.grid.shape}):")
            print(executor.grid)
        else:
            print("Grille: None")
    
    print(f"\n=== RÉSULTAT FINAL ===")
    print(f"Succès: {executor.error is None}")
    if executor.error:
        print(f"Erreur: {executor.error}")
    
    if executor.grid is not None:
        print(f"Grille finale:")
        print(executor.grid)
        print(f"Grille finale (liste):")
        print(executor.grid.tolist())
    
    return executor.grid.tolist() if executor.grid is not None else None

def test_replace_isolated():
    """Test isolé de la commande REPLACE"""
    
    print("\n=== TEST ISOLÉ DE REPLACE ===")
    
    executor = CommandExecutor()
    
    # Créer une grille de test simple
    executor._execute_command('INIT 3 3')
    print("Grille initiale:")
    print(executor.grid)
    
    # Ajouter quelques valeurs 1
    executor._execute_command('EDIT 1 [0,0]')
    executor._execute_command('EDIT 1 [0,2]') 
    executor._execute_command('EDIT 1 [2,0]')
    executor._execute_command('EDIT 1 [2,2]')
    
    print("\nGrille avec des 1:")
    print(executor.grid)
    
    # Tester REPLACE
    success = executor._execute_command('REPLACE 1 2 [0,0 2,2]')
    
    print(f"\nRESULT REPLACE: {success}")
    if not success:
        print(f"Erreur: {executor.error}")
    
    print("Grille après REPLACE:")
    print(executor.grid)
    
    return executor.grid.tolist() if executor.grid is not None else None

def analyze_coordinate_parsing():
    """Analyser le parsing des coordonnées pour REPLACE"""
    
    print("\n=== ANALYSE DU PARSING DES COORDONNÉES ===")
    
    executor = CommandExecutor()
    
    # Test du parsing
    raw_command = 'REPLACE 1 2 [0,0 2,2]'
    coordinate_blocks = executor._parse_coordinate_blocks(raw_command)
    
    print(f"Commande: {raw_command}")
    print(f"Blocs de coordonnées parsés: {coordinate_blocks}")
    
    # Test de la fonction de traitement
    def debug_action(x1, y1, x2, y2):
        print(f"Action appelée avec: x1={x1}, y1={y1}, x2={x2}, y2={y2}")
    
    print("\nTraitement des blocs:")
    executor.grid = np.array([[1, 0, 1], [0, 0, 0], [1, 0, 1]])
    executor._process_coordinate_blocks(coordinate_blocks, debug_action)

if __name__ == "__main__":
    # Test de la séquence complète
    result = test_replace_sequence()
    
    # Test isolé
    test_replace_isolated()
    
    # Analyse du parsing
    analyze_coordinate_parsing()
    
    print(f"\n=== GRILLE ATTENDUE ===")
    expected = [[2, 0, 2], [0, 0, 0], [0, 0, 0]]
    print(expected)
    
    if result:
        print(f"\n=== COMPARAISON ===")
        print(f"Obtenu:  {result}")
        print(f"Attendu: {expected}")
        print(f"Match: {result == expected}")