// src/components/resolution/hooks/useAutomation.ts
import { useCallback } from 'react';
import { useGridStateStore } from './useGridStateStore';
import { useSelection } from './useSelection';
import { useClipboard } from './useClipboard';
import { useAutomationStore } from './useAutomationStore';
import { Grid } from '../../../lib/grid';
import { UnifiedPlaybackCommand } from '../utils/commandGenerationUtils';
import { deleteRows, deleteColumns, insertRows, insertColumns } from '../utils/gridOperations';
// Removed unused import

// ==================================================================
// PHASE 4: NOUVEAU SYSTÈME D'EXÉCUTION DE COMMANDES UNIFIÉES
// ==================================================================

// Hook principal pour l'automation (save/load)
export function useAutomation() {
    const saveToFile = useCallback(() => {
        // TODO: Implementer la sauvegarde vers fichier
        console.log('Save to file not yet implemented');
    }, []);

    const loadFromFile = useCallback(() => {
        // TODO: Implementer le chargement depuis fichier
        console.log('Load from file not yet implemented');
    }, []);

    return {
        saveToFile,
        loadFromFile
    };
}

// ==================================================================
// FONCTION GÉNÉRIQUE POUR PARSER LES BLOCS DE COORDONNÉES
// ==================================================================

/**
 * Extrait les blocs de coordonnées directement depuis rawCommand sans reformatage
 * Traite chaque bloc individuellement : [1,1 4,2] reste ["1,1", "4,2"]
 * Gère directement les modificateurs INVERT et COLOR
 */
function extractCoordinateBlocksDirectly(rawCommand: string): string[][] {
    const coordinateBlocks: string[][] = [];

    if (!rawCommand) return coordinateBlocks;

    // Gestion des modificateurs spéciaux (INVERT, COLOR)

    // Détecter (INVERT ([...])) ou (INVERT [...])
    const invertPatternDouble = /\(INVERT\s*\(([^)]+)\)\)/;
    const invertPatternSingle = /\(INVERT\s*\[([^\]]+)\]\)/;

    let invertMatch = rawCommand.match(invertPatternDouble);
    let invertContent = null;

    if (invertMatch) {
        invertContent = invertMatch[1];
    } else {
        invertMatch = rawCommand.match(invertPatternSingle);
        if (invertMatch) {
            invertContent = `[${invertMatch[1]}]`; // Remettre les crochets pour uniformiser
        }
    }

    if (invertContent) {
        // Extraire TOUTES les coordonnées pour créer UN SEUL bloc INVERT
        const coordPattern = /\[([^\]]+)\]/g;
        const allCoords = [];
        let coordMatch;
        while ((coordMatch = coordPattern.exec(invertContent)) !== null) {
            const blockContent = coordMatch[1].trim();
            if (blockContent.includes(' ')) {
                const coords = blockContent.split(/\s+/).filter(c => c.trim() !== '');
                allCoords.push(...coords);
            } else {
                allCoords.push(blockContent);
            }
        }
        // Créer UN SEUL bloc INVERT avec toutes les coordonnées
        if (allCoords.length > 0) {
            coordinateBlocks.push(['INVERT', ...allCoords]);
        }
        return coordinateBlocks;
    }

    // Détecter les opérateurs XOR, AND, OR (nouveau format sans parenthèses)
    const xorPattern = /^XOR\s+(.+)$/;
    const andPattern = /^AND\s+(.+)$/;
    const orPattern = /^OR\s+(.+)$/;

    // Vérifier XOR
    let xorMatch = rawCommand.match(xorPattern);
    if (xorMatch) {
        const coordinates = xorMatch[1].trim();
        // Pour XOR, on s'attend à avoir exactement 2 blocs de coordonnées
        const coordBlocks = extractCoordinateBlocksFromGrouped(coordinates);
        if (coordBlocks.length === 2) {
            const resolvedCoords = resolveXorOperator(coordBlocks[0].join(' '), coordBlocks[1].join(' '));
            if (resolvedCoords.length > 0) {
                coordinateBlocks.push(resolvedCoords);
            }
        }
        return coordinateBlocks;
    }

    // Vérifier AND
    let andMatch = rawCommand.match(andPattern);
    if (andMatch) {
        const coordinates = andMatch[1].trim();
        const coordBlocks = extractCoordinateBlocksFromGrouped(coordinates);
        const operands = coordBlocks.map(block => block.join(' '));
        const resolvedCoords = resolveAndOperator(operands);
        if (resolvedCoords.length > 0) {
            coordinateBlocks.push(resolvedCoords);
        }
        return coordinateBlocks;
    }

    // Vérifier OR
    let orMatch = rawCommand.match(orPattern);
    if (orMatch) {
        const coordinates = orMatch[1].trim();
        const coordBlocks = extractCoordinateBlocksFromGrouped(coordinates);
        const operands = coordBlocks.map(block => block.join(' '));
        const resolvedCoords = resolveOrOperator(operands);
        if (resolvedCoords.length > 0) {
            coordinateBlocks.push(resolvedCoords);
        }
        return coordinateBlocks;
    }

    // Détecter (COLOR params ([...])) ou (COLOR params [...])
    const colorPatternDouble = /\(COLOR\s+([^(]+)\s*\(([^)]+)\)\)/;
    const colorPatternSingle = /\(COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)/;

    let colorMatch = rawCommand.match(colorPatternDouble);
    let colorParams = null;
    let colorContent = null;

    if (colorMatch) {
        colorParams = colorMatch[1].trim();
        colorContent = colorMatch[2];
    } else {
        colorMatch = rawCommand.match(colorPatternSingle);
        if (colorMatch) {
            colorParams = colorMatch[1].trim();
            colorContent = `[${colorMatch[2]}]`; // Remettre les crochets pour uniformiser
        }
    }

    if (colorParams && colorContent) {
        // Extraire TOUTES les coordonnées pour créer UN SEUL bloc COLOR
        const coordPattern = /\[([^\]]+)\]/g;
        const allCoords = [];
        let coordMatch;
        while ((coordMatch = coordPattern.exec(colorContent)) !== null) {
            const blockContent = coordMatch[1].trim();
            if (blockContent.includes(' ')) {
                const coords = blockContent.split(/\s+/).filter(c => c.trim() !== '');
                allCoords.push(...coords);
            } else {
                allCoords.push(blockContent);
            }
        }
        // Créer UN SEUL bloc COLOR avec toutes les coordonnées
        if (allCoords.length > 0) {
            coordinateBlocks.push(['COLOR', colorParams, ...allCoords]);
        }
        return coordinateBlocks;
    }

    // Extraction directe des blocs entre crochets sans reformatage
    const blockRegex = /\[([^\]]+)\]/g;
    let match;

    while ((match = blockRegex.exec(rawCommand)) !== null) {
        const blockContent = match[1].trim();

        if (blockContent.includes(' ')) {
            // Bloc avec plusieurs coordonnées : "1,1 4,2" -> ["1,1", "4,2"]
            const coords = blockContent.split(/\s+/).filter(c => c.trim() !== '');
            coordinateBlocks.push(coords);
        } else {
            // Bloc avec une seule coordonnée : "0,0" -> ["0,0"]
            coordinateBlocks.push([blockContent]);
        }
    }

    return coordinateBlocks;
}






/**
 * Applique une action sur chaque bloc de coordonnées
 * @param coordinateBlocks - Blocs de coordonnées parsés
 * @param grid - Grille courante
 * @param action - Fonction à appliquer pour chaque bloc (cellule ou rectangle)
 */
function processCoordinateBlocks(
    coordinateBlocks: string[][],
    grid: Grid,
    action: (row1: number, col1: number, row2: number, col2: number) => void
): void {
    for (const block of coordinateBlocks) {
        // Détecter les modificateurs de coordonnées
        if (block.length > 0 && block[0] === 'INVERT') {
            // Bloc avec modificateur INVERT - traiter toutes les coordonnées comme un ensemble
            const coords = block.slice(1); // Enlever le premier élément 'INVERT'

            // Créer un Set de toutes les cellules à exclure
            const excludedCells = new Set<string>();

            // Traiter les coordonnées par paires pour créer des rectangles/cellules
            for (let i = 0; i < coords.length; i += 2) {
                if (i + 1 < coords.length) {
                    // Rectangle
                    const [row1, col1] = coords[i].split(',').map(Number);
                    const [row2, col2] = coords[i + 1].split(',').map(Number);

                    if (!isNaN(row1) && !isNaN(col1) && !isNaN(row2) && !isNaN(col2) &&
                        row1 >= 0 && row1 < grid.height && row2 >= 0 && row2 < grid.height &&
                        col1 >= 0 && col1 < grid.width && col2 >= 0 && col2 < grid.width) {

                        const minRow = Math.min(row1, row2);
                        const maxRow = Math.max(row1, row2);
                        const minCol = Math.min(col1, col2);
                        const maxCol = Math.max(col1, col2);

                        // Ajouter toutes les cellules du rectangle à l'ensemble exclu
                        for (let r = minRow; r <= maxRow; r++) {
                            for (let c = minCol; c <= maxCol; c++) {
                                excludedCells.add(`${r},${c}`);
                            }
                        }
                    }
                } else {
                    // Cellule simple
                    const [row, col] = coords[i].split(',').map(Number);
                    if (!isNaN(row) && !isNaN(col) &&
                        row >= 0 && row < grid.height &&
                        col >= 0 && col < grid.width) {
                        excludedCells.add(`${row},${col}`);
                    }
                }
            }

            // Appliquer l'action à toutes les cellules SAUF celles dans l'ensemble exclu
            for (let row = 0; row < grid.height; row++) {
                for (let col = 0; col < grid.width; col++) {
                    if (!excludedCells.has(`${row},${col}`)) {
                        action(row, col, row, col);
                    }
                }
            }
        } else if (block.length > 0 && block[0] === 'COLOR') {
            // Bloc avec modificateur COLOR
            if (block.length >= 3) {
                const colorParams = block[1];
                const colors = colorParams.split(',').map(c => parseInt(c.trim(), 10)).filter(c => !isNaN(c));

                if (block.length === 3) {
                    // Cellule simple avec COLOR
                    const [row, col] = block[2].split(',').map(Number);
                    if (!isNaN(row) && !isNaN(col) &&
                        row >= 0 && row < grid.height &&
                        col >= 0 && col < grid.width) {
                        // Appliquer l'action seulement si la cellule a la bonne couleur
                        const cellColor = grid.getCell(row, col) ?? 0;
                        if (colors.includes(cellColor)) {
                            action(row, col, row, col);
                        }
                    }
                } else if (block.length === 4) {
                    // Rectangle avec COLOR
                    const [row1, col1] = block[2].split(',').map(Number);
                    const [row2, col2] = block[3].split(',').map(Number);

                    if (!isNaN(row1) && !isNaN(col1) && !isNaN(row2) && !isNaN(col2) &&
                        row1 >= 0 && row1 < grid.height && row2 >= 0 && row2 < grid.height &&
                        col1 >= 0 && col1 < grid.width && col2 >= 0 && col2 < grid.width) {
                        const minRow = Math.min(row1, row2);
                        const maxRow = Math.max(row1, row2);
                        const minCol = Math.min(col1, col2);
                        const maxCol = Math.max(col1, col2);

                        // Appliquer l'action seulement aux cellules des couleurs spécifiées dans le rectangle
                        for (let row = minRow; row <= maxRow; row++) {
                            for (let col = minCol; col <= maxCol; col++) {
                                const cellColor = grid.getCell(row, col) ?? 0;
                                if (colors.includes(cellColor)) {
                                    action(row, col, row, col);
                                }
                            }
                        }
                    }
                }
            }
        } else if (block.length === 1) {
            // Cellule simple traditionnelle
            const [row, col] = block[0].split(',').map(Number);
            if (!isNaN(row) && !isNaN(col) &&
                row >= 0 && row < grid.height &&
                col >= 0 && col < grid.width) {
                action(row, col, row, col);
            }
        } else if (block.length === 2) {
            // Rectangle traditionnel
            const [row1, col1] = block[0].split(',').map(Number);
            const [row2, col2] = block[1].split(',').map(Number);

            if (!isNaN(row1) && !isNaN(col1) && !isNaN(row2) && !isNaN(col2) &&
                row1 >= 0 && row1 < grid.height && row2 >= 0 && row2 < grid.height &&
                col1 >= 0 && col1 < grid.width && col2 >= 0 && col2 < grid.width) {
                action(row1, col1, row2, col2);
            }
        }
    }
}


// ==================================================================
// IMPLEMENTATIONS SPÉCIFIQUES REFACTORISÉES
// ==================================================================

// --- Implémentations spécifiques (helpers) ---
async function executeUnifiedInit(parameters: string | undefined, _grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    try {
        if (!parameters) {
            console.error('INIT: paramètres manquants');
            return false;
        }

        // Les paramètres contiennent directement les dimensions : "3x3"
        const match = parameters.trim().match(/(\d+)x(\d+)/);
        if (match) {
            const width = parseInt(match[1]);
            const height = parseInt(match[2]);
            const newGrid = new Grid(height, width);
            console.log(`[INIT] ✅ Grille ${width}x${height}`);
            setGrid(newGrid);
            return true;
        } else {
            console.error(`[INIT] Format de paramètres invalide: ${parameters}`);
            return false;
        }

    } catch (error) {
        console.error('Erreur executeUnifiedInit:', error);
        return false;
    }
}

async function executeUnifiedFill(colorParam: string | undefined, _coordinates: string[], _additionalCoords: string[], grid: Grid | null, setGrid: (g: Grid) => void, rawCommand?: string): Promise<boolean> {
    // Utiliser la grille passée en paramètre ou obtenir la grille actuelle
    const currentGrid = grid || useGridStateStore.getState().getCurrentGrid();

    if (!currentGrid || colorParam === undefined) return false;
    try {
        const color = parseInt(colorParam, 10);
        if (isNaN(color)) return false;

        const newGrid = currentGrid.clone();

        // Utiliser l'extraction directe des blocs de coordonnées
        const coordinateBlocks = rawCommand ?
            extractCoordinateBlocksDirectly(rawCommand) :
            [];

        console.log(`[FILL] Traitement de ${coordinateBlocks.length} blocs de coordonnées:`, coordinateBlocks);

        // Utiliser la fonction générique pour traiter les blocs
        processCoordinateBlocks(coordinateBlocks, newGrid, (row1, col1, row2, col2) => {
            const minRow = Math.min(row1, row2);
            const maxRow = Math.max(row1, row2);
            const minCol = Math.min(col1, col2);
            const maxCol = Math.max(col1, col2);

            for (let r = minRow; r <= maxRow; r++) {
                for (let c = minCol; c <= maxCol; c++) {
                    newGrid.setCell(r, c, color);
                }
            }

            if (row1 === row2 && col1 === col2) {
                console.log(`[FILL] Cellule remplie: (${row1},${col1}) avec couleur ${color}`);
            } else {
                console.log(`[FILL] Rectangle rempli: (${minRow},${minCol}) -> (${maxRow},${maxCol}) avec couleur ${color}`);
            }
        });

        setGrid(newGrid);
        return true;
    } catch (error) {
        console.error('Erreur executeUnifiedFill:', error);
        return false;
    }
}

async function executeUnifiedClear(coordinates: string[], additionalCoords: string[], _grid: Grid | null, setGrid: (g: Grid) => void, rawCommand?: string): Promise<boolean> {
    // Obtenir la grille la plus récente
    const { getCurrentGrid } = useGridStateStore.getState();
    const grid = getCurrentGrid();

    if (!grid) return false;

    try {
        const newGrid = grid.clone();

        // Utiliser l'extraction directe des blocs de coordonnées
        const coordinateBlocks = rawCommand ?
            extractCoordinateBlocksDirectly(rawCommand) :
            [];

        console.log(`[CLEAR] Traitement de ${coordinateBlocks.length} blocs de coordonnées:`, coordinateBlocks);

        // Utiliser la fonction générique pour traiter les blocs (effacer = remplir avec 0)
        processCoordinateBlocks(coordinateBlocks, newGrid, (row1, col1, row2, col2) => {
            const minRow = Math.min(row1, row2);
            const maxRow = Math.max(row1, row2);
            const minCol = Math.min(col1, col2);
            const maxCol = Math.max(col1, col2);

            for (let r = minRow; r <= maxRow; r++) {
                for (let c = minCol; c <= maxCol; c++) {
                    newGrid.setCell(r, c, 0);
                }
            }

            if (row1 === row2 && col1 === col2) {
                console.log(`[CLEAR] Cellule effacée: (${row1},${col1})`);
            } else {
                console.log(`[CLEAR] Rectangle effacé: (${minRow},${minCol}) -> (${maxRow},${maxCol})`);
            }
        });

        setGrid(newGrid);
        return true;
    } catch (error) {
        console.error('Erreur executeUnifiedClear:', error);
        return false;
    }
}

async function executeUnifiedSelect(coordinates: string[], additionalCoords: string[]): Promise<boolean> {
    try {
        const allCoords = [...coordinates, ...additionalCoords];
        const positions = allCoords.map(coord => {
            const [row, col] = coord.split(',').map(Number);
            return { row, col };
        });

        const { clearSelection, selectCell } = useSelection.getState();
        clearSelection(); // SELECT remplace la sélection actuelle
        positions.forEach(pos => {
            if (!isNaN(pos.row) && !isNaN(pos.col)) {
                selectCell(pos.row, pos.col, 'add');
            }
        });
        return true;
    } catch (error) {
        console.error('Erreur executeUnifiedSelect:', error);
        return false;
    }
}

async function executeUnifiedInsert(params: string | undefined, coordinates: string[], _grid: Grid | null, _setGrid: (g: Grid) => void): Promise<boolean> {
    if (!params || coordinates.length < 1) return false;
    console.log(`[INSERT] ${params} ${coordinates}`);

    const paramParts = params.split(' ');
    const count = parseInt(paramParts[0]) || 1;
    const type = paramParts[1]; // ROWS ou COLUMNS
    const direction = paramParts[2]; // AFTER, BEFORE, etc.

    console.log(`[INSERT] Parsed: count=${count}, type=${type}, direction=${direction}`);

    if (!type || !['ROWS', 'COLUMNS'].includes(type)) {
        console.error(`[INSERT] Type invalide: ${type}`);
        return false;
    }

    // Valider les directions selon le type
    if (type === 'ROWS') {
        if (!['ABOVE', 'BELOW'].includes(direction)) {
            console.error(`[INSERT] Direction invalide pour ROWS: ${direction}. Utilisez ABOVE ou BELOW`);
            return false;
        }
    } else if (type === 'COLUMNS') {
        if (!['BEFORE', 'AFTER'].includes(direction)) {
            console.error(`[INSERT] Direction invalide pour COLUMNS: ${direction}. Utilisez BEFORE ou AFTER`);
            return false;
        }
    }

    // Gérer les coordonnées selon le format unifié
    let startRow, startCol, endRow, endCol;

    if (coordinates.length === 1) {
        // Une seule coordonnée - cellule [2,6]
        [startRow, startCol] = coordinates[0].split(',').map(Number);
        endRow = startRow;
        endCol = startCol;
    } else if (coordinates.length === 2) {
        // Deux coordonnées - rectangle [7,4 9,9] parsé en ["7,4", "9,9"]
        [startRow, startCol] = coordinates[0].split(',').map(Number);
        [endRow, endCol] = coordinates[1].split(',').map(Number);
    } else {
        console.error(`[INSERT] Format de coordonnées invalide, reçu ${coordinates.length} éléments: ${JSON.stringify(coordinates)}`);
        return false;
    }

    if ([startRow, startCol, endRow, endCol].some(isNaN)) {
        console.error(`[INSERT] Coordonnées invalides: ${coordinates}`);
        return false;
    }

    const RectSelection = { startRow, startCol, endRow, endCol };
    console.log(`[INSERT] RectSelection: ${JSON.stringify(RectSelection)}`);

    try {
        console.log(`[INSERT] Appel de la fonction d'insertion: ${type} avec RectSelection=${JSON.stringify(RectSelection)}, direction=${direction}, count=${count}`);

        if (type === 'ROWS') {
            insertRows(RectSelection, direction, count);
            console.log(`[INSERT] insertRows terminé avec succès`);
        } else if (type === 'COLUMNS') {
            insertColumns(RectSelection, direction, count);
            console.log(`[INSERT] insertColumns terminé avec succès`);
        } else {
            console.error(`[INSERT] Type non supporté: ${type}`);
            return false;
        }
        console.log(`[INSERT] Succès: ${count} ${type} ${direction}`);
        return true;
    } catch (error) {
        console.error(`[INSERT] Erreur lors de l'exécution:`, error);
        return false;
    }
}

async function executeUnifiedDelete(params: string | undefined, coordinates: string[], _grid: Grid | null, _setGrid: (g: Grid) => void): Promise<boolean> {
    if (!params || coordinates.length < 2) return false;
    console.log(`[DELETE] ${params} ${coordinates}`);

    const [type] = params.split(' ');

    const [startRow, startCol] = coordinates[0].split(',').map(Number);
    const [endRow, endCol] = coordinates[1].split(',').map(Number);

    if ([startRow, startCol, endRow, endCol].some(isNaN)) return false;

    const RectSelection = { startRow, startCol, endRow, endCol };
    console.log(`[DELETE] executeUnifiedDelete: DELETE ${type} [${RectSelection.startRow},${RectSelection.startCol} ${RectSelection.endRow},${RectSelection.endCol}]`);

    if (type === 'ROWS') {
        deleteRows(RectSelection);
    } else if (type === 'COLUMNS') {
        deleteColumns(RectSelection);
    } else {
        console.error(`[DELETE] Type de suppression non supporté: ${type}`);
        return false;
    }
    return true;
}

async function executeUnifiedExtract(coordinates: string[], _grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    // Obtenir la grille la plus récente
    const { getCurrentGrid } = useGridStateStore.getState();
    const grid = getCurrentGrid();

    if (!grid || coordinates.length < 2) return false;
    const [startRow, startCol] = coordinates[0].split(',').map(Number);
    const [endRow, endCol] = coordinates[1].split(',').map(Number);
    if ([startRow, startCol, endRow, endCol].some(isNaN)) return false;

    const minRow = Math.min(startRow, endRow);
    const maxRow = Math.max(startRow, endRow);
    const minCol = Math.min(startCol, endCol);
    const maxCol = Math.max(startCol, endCol);

    const height = maxRow - minRow + 1;
    const width = maxCol - minCol + 1;
    const newGrid = new Grid(height, width);
    for (let r = 0; r < height; r++) {
        for (let c = 0; c < width; c++) {
            newGrid.setCell(r, c, grid.getCell(minRow + r, minCol + c) ?? 0);
        }
    }
    setGrid(newGrid);
    return true;
}

async function executeUnifiedMultiply(parameters: string | undefined, coordinates: string[], _grid: Grid | null, _setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[MULTIPLY] Début avec paramètres: ${parameters}, coordonnées: ${JSON.stringify(coordinates)}`);

    // Vérifier qu'il y a du contenu dans le presse-papier
    const clipboardState = useClipboard.getState();
    console.log(`[MULTIPLY] Contenu du presse-papier:`, clipboardState.content);

    if (!clipboardState.content) {
        console.error('[MULTIPLY] Aucun contenu dans le presse-papier. Utilisez COPY avant MULTIPLY.');
        return false;
    }

    if (!parameters) {
        console.error('[MULTIPLY] Paramètres invalides');
        return false;
    }

    try {
        // Parser les paramètres : "3" ou "3 true" ou "3 false"
        const params = parameters.split(' ');
        const factor = parseInt(params[0]);
        if (isNaN(factor) || factor < 2 || factor > 10) {
            console.error('[MULTIPLY] Facteur invalide:', factor);
            return false;
        }

        // Parser le paramètre replace_content (par défaut true)
        let replaceContent = true;
        if (params.length >= 2) {
            const replaceParam = params[1].toLowerCase();
            if (replaceParam === 'false' || replaceParam === '0' || replaceParam === 'no') {
                replaceContent = false;
            } else if (replaceParam === 'true' || replaceParam === '1' || replaceParam === 'yes') {
                replaceContent = true;
            } else {
                console.error('[MULTIPLY] Paramètre replace_content invalide:', replaceParam);
                return false;
            }
        }

        console.log(`[MULTIPLY] Facteur: ${factor}, replace_content: ${replaceContent}`);

        // Utiliser la fonction multiplyClipboard pour multiplier le contenu du presse-papier
        const { multiplyClipboard } = await import('../utils/clipboardTransformUtils');
        const selectionStore = useSelection.getState();
        const automationStore = useAutomationStore.getState();

        multiplyClipboard(
            factor,
            useClipboard.setState,
            useClipboard.getState,
            selectionStore,
            automationStore,
            replaceContent
        );

        console.log(`[MULTIPLY] ✅ Multiplication par ${factor} terminée, nouveau contenu:`, useClipboard.getState().content);
        return true;

    } catch (error) {
        console.error('[MULTIPLY] Erreur:', error);
        return false;
    }
}

async function executeUnifiedDivide(parameters: string | undefined, coordinates: string[], _grid: Grid | null, _setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[DIVIDE] Début avec paramètres: ${parameters}, coordonnées: ${JSON.stringify(coordinates)}`);

    // Vérifier qu'il y a du contenu dans le presse-papier
    const clipboardState = useClipboard.getState();
    console.log(`[DIVIDE] Contenu du presse-papier:`, clipboardState.content);

    if (!clipboardState.content) {
        console.error('[DIVIDE] Aucun contenu dans le presse-papier. Utilisez COPY avant DIVIDE.');
        return false;
    }

    if (!parameters) {
        console.error('[DIVIDE] Paramètres invalides');
        return false;
    }

    try {
        const factor = parseInt(parameters);
        if (isNaN(factor) || factor < 2 || factor > 10) {
            console.error('[DIVIDE] Facteur invalide:', factor);
            return false;
        }

        // Utiliser la fonction divideClipboard pour diviser le contenu du presse-papier
        const { divideClipboard } = await import('../utils/clipboardTransformUtils');
        const selectionStore = useSelection.getState();
        const automationStore = useAutomationStore.getState();

        divideClipboard(
            factor,
            useClipboard.setState,
            useClipboard.getState,
            selectionStore,
            automationStore
        );

        console.log(`[DIVIDE] ✅ Division par ${factor} terminée, nouveau contenu:`, useClipboard.getState().content);
        return true;

    } catch (error) {
        console.error('[DIVIDE] Erreur:', error);
        return false;
    }
}

async function executeUnifiedFlip(direction: string | undefined, _coordinates: string[], _grid: Grid | null, _setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[FLIP] Début avec direction: ${direction}, coordonnées: ${JSON.stringify(_coordinates)}`);

    if (!direction) {
        console.error(`[FLIP] Direction non fournie`);
        return false;
    }

    // Flipper le contenu du presse-papier (les coordonnées sont ignorées car FLIP s'applique toujours au motif dans le presse-papier)
    const { content } = useClipboard.getState();

    if (!content || !content.data) {
        console.error(`[FLIP] Aucun contenu dans le presse-papier à flipper`);
        return false;
    }

    console.log(`[FLIP] Flip du contenu du presse-papier`);
    console.log(`[FLIP] Contenu du presse-papier avant flip:`, content.data);

    // Appliquer le flip au contenu du presse-papier
    let flippedClipboard;
    let flippedMask;

    if (direction.toUpperCase() === 'HORIZONTAL') {
        console.log(`[FLIP] Application du flip HORIZONTAL au presse-papier`);
        flippedClipboard = content.data.map(row => [...row].reverse());

        // Flip du masque si présent
        if (content.mask) {
            flippedMask = content.mask.map(row => [...row].reverse());
        }
    } else if (direction.toUpperCase() === 'VERTICAL') {
        console.log(`[FLIP] Application du flip VERTICAL au presse-papier`);
        flippedClipboard = [...content.data].reverse();

        // Flip du masque si présent
        if (content.mask) {
            flippedMask = [...content.mask].reverse();
        }
    } else {
        console.error(`[FLIP] Direction invalide: ${direction}. Utilisez HORIZONTAL ou VERTICAL.`);
        return false;
    }

    console.log(`[FLIP] Contenu du presse-papier après flip:`, flippedClipboard);
    console.log(`[FLIP] Masque après flip:`, flippedMask);

    // Validation des dimensions après flip
    const newWidth = flippedClipboard[0]?.length || 0;
    const newHeight = flippedClipboard.length;

    console.log(`[FLIP] Validation dimensions après flip: data.length=${newHeight}, height=${newHeight}, width=${newWidth}`);

    if (newHeight === 0 || newWidth === 0) {
        console.error(`[FLIP] Dimensions invalides après flip: ${newWidth}x${newHeight}`);
        return false;
    }

    // Validation du masque si présent
    if (flippedMask && flippedMask.length !== newHeight) {
        console.error(`[FLIP] Incohérence masque après flip: mask.length=${flippedMask.length}, height=${newHeight}`);
        return false;
    }

    const updatedContent = {
        ...content,
        data: flippedClipboard,
        width: newWidth,
        height: newHeight
    };

    // Ajouter le masque flippé si présent
    if (flippedMask) {
        updatedContent.mask = flippedMask;
    }

    // Mettre à jour le presse-papier avec le contenu flippé
    useClipboard.setState({
        ...useClipboard.getState(),
        content: updatedContent
    });

    // Validation finale
    const finalContent = useClipboard.getState().content;
    if (finalContent?.data?.length !== finalContent?.height) {
        console.error(`[FLIP] ERREUR: Incohérence après flip du presse-papier!`, {
            dataLength: finalContent?.data?.length,
            height: finalContent?.height,
            width: finalContent?.width
        });
        return false;
    }

    console.log(`[FLIP] ✅ Flip ${direction} terminé`);
    return true;
}

async function executeUnifiedRotate(direction: string | undefined, coordinates: string[], _grid: Grid | null, _setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[ROTATE] Début avec direction: ${direction}, coordonnées: ${JSON.stringify(coordinates)}`);

    if (!direction) {
        console.error(`[ROTATE] Direction non fournie`);
        return false;
    }

    // Rotation du contenu du presse-papier (les coordonnées sont ignorées car ROTATE s'applique toujours au motif dans le presse-papier)
    const { content } = useClipboard.getState();

    if (!content || !content.data) {
        console.error(`[ROTATE] Aucun contenu dans le presse-papier à tourner`);
        return false;
    }

    console.log(`[ROTATE] Rotation du contenu du presse-papier`);
    console.log(`[ROTATE] Contenu du presse-papier avant rotation:`, content.data);

    // Appliquer la rotation au contenu du presse-papier
    let rotatedClipboard;
    let rotatedMask;

    if (direction.toUpperCase() === 'RIGHT') {
        console.log(`[ROTATE] Application de la rotation RIGHT au presse-papier`);
        rotatedClipboard = content.data[0].map((_, colIndex) => content.data.map(row => row[colIndex]));
        rotatedClipboard.forEach(row => row.reverse());

        // Rotation du masque si présent
        if (content.mask) {
            rotatedMask = content.mask[0].map((_, colIndex) => content.mask.map(row => row[colIndex]));
            rotatedMask.forEach(row => row.reverse());
        }
    } else if (direction.toUpperCase() === 'LEFT') {
        console.log(`[ROTATE] Application de la rotation LEFT au presse-papier`);
        rotatedClipboard = content.data[0].map((_, colIndex) => content.data.map(row => row[colIndex]));
        rotatedClipboard.reverse();

        // Rotation du masque si présent
        if (content.mask) {
            rotatedMask = content.mask[0].map((_, colIndex) => content.mask.map(row => row[colIndex]));
            rotatedMask.reverse();
        }
    } else {
        console.error(`[ROTATE] Direction invalide: ${direction}. Utilisez RIGHT ou LEFT.`);
        return false;
    }

    console.log(`[ROTATE] Contenu du presse-papier après rotation:`, rotatedClipboard);
    console.log(`[ROTATE] Masque après rotation:`, rotatedMask);

    // Mettre à jour le presse-papier avec le contenu tourné
    // Vérifier la cohérence des dimensions avant la mise à jour
    const newWidth = rotatedClipboard[0]?.length || 0;
    const newHeight = rotatedClipboard.length;

    console.log(`[ROTATE] Validation dimensions après rotation: data.length=${newHeight}, height=${newHeight}, width=${newWidth}`);

    if (newHeight === 0 || newWidth === 0) {
        console.error(`[ROTATE] Dimensions invalides après rotation: ${newWidth}x${newHeight}`);
        return false;
    }

    // Validation du masque si présent
    if (rotatedMask && rotatedMask.length !== newHeight) {
        console.error(`[ROTATE] Incohérence masque après rotation: mask.length=${rotatedMask.length}, height=${newHeight}`);
        return false;
    }

    const updatedContent = {
        ...content,
        data: rotatedClipboard,
        width: newWidth,
        height: newHeight
    };

    // Ajouter le masque tourné si présent
    if (rotatedMask) {
        updatedContent.mask = rotatedMask;
    }

    // Mettre à jour le presse-papier avec le contenu tourné
    useClipboard.setState({
        ...useClipboard.getState(),
        content: updatedContent
    });

    // Validation finale
    const finalContent = useClipboard.getState().content;
    if (finalContent?.data?.length !== finalContent?.height) {
        console.error(`[ROTATE] ERREUR: Incohérence après rotation du presse-papier!`, {
            dataLength: finalContent?.data?.length,
            height: finalContent?.height,
            width: finalContent?.width
        });
        return false;
    }

    console.log(`[ROTATE] ✅ Rotation ${direction} terminée`);
    return true;
}

async function executeUnifiedFloodFill(parameters: string | undefined, coordinates: string[], additionalCoords: string[], grid: Grid | null, setGrid: (g: Grid) => void, rawCommand?: string): Promise<boolean> {
    console.log(`[FLOODFILL] Début avec paramètres: ${parameters}, coordonnées: ${JSON.stringify(coordinates)}`);

    // Obtenir la grille la plus récente
    const { getCurrentGrid } = useGridStateStore.getState();
    let currentGrid = grid || getCurrentGrid();

    if (!currentGrid || !parameters) {
        console.error('[FLOODFILL] Paramètres manquants ou grille invalide');
        return false;
    }

    // Le paramètre contient la couleur à utiliser pour le remplissage
    const fillColor = parseInt(parameters.trim(), 10);
    if (isNaN(fillColor)) {
        console.error(`[FLOODFILL] Couleur invalide: ${parameters}`);
        return false;
    }

    // Utiliser l'extraction directe des blocs de coordonnées
    const coordinateBlocks = rawCommand ?
        extractCoordinateBlocksDirectly(rawCommand) :
        // Fallback: traiter toutes les coordonnées comme des cellules individuelles
        [...coordinates, ...additionalCoords].map(coord => [coord]);

    console.log(`[FLOODFILL] Traitement de ${coordinateBlocks.length} blocs de coordonnées:`, coordinateBlocks);

    // Utiliser la logique de flood fill des utils
    const { floodFillLogic } = await import('../utils/gridUtils');
    let totalChangedCells = 0;

    // Traiter chaque bloc de coordonnées (généralement des cellules individuelles pour FLOODFILL)
    for (const block of coordinateBlocks) {
        if (!currentGrid) break; // Protection contre null

        processCoordinateBlocks([block], currentGrid, (row1, col1, row2, col2) => {
            if (!currentGrid) return; // Protection supplémentaire

            // Pour FLOODFILL, traiter chaque cellule du bloc
            const minRow = Math.min(row1, row2);
            const maxRow = Math.max(row1, row2);
            const minCol = Math.min(col1, col2);
            const maxCol = Math.max(col1, col2);

            for (let row = minRow; row <= maxRow; row++) {
                for (let col = minCol; col <= maxCol; col++) {
                    console.log(`[FLOODFILL] Remplissage à partir de (${row},${col}) avec la couleur ${fillColor}`);

                    // Obtenir la couleur actuelle à la position
                    const currentColor = currentGrid.getCell(row, col);
                    console.log(`[FLOODFILL] Couleur actuelle en (${row},${col}): ${currentColor}, nouvelle couleur: ${fillColor}`);

                    if (currentColor === fillColor) {
                        console.log(`[FLOODFILL] Aucun changement nécessaire pour (${row},${col}), couleur déjà présente`);
                        return;
                    }

                    // Appliquer le flood fill
                    const newGrid = floodFillLogic(currentGrid, row, col, fillColor);

                    // Compter les cellules qui ont changé pour cette itération
                    let changedCells = 0;
                    for (let r = 0; r < currentGrid.height; r++) {
                        for (let c = 0; c < currentGrid.width; c++) {
                            if (currentGrid.getCell(r, c) !== newGrid.getCell(r, c)) {
                                changedCells++;
                                totalChangedCells++;
                                if (totalChangedCells <= 10) { // Afficher seulement les 10 premières pour éviter le spam
                                    console.log(`[FLOODFILL] Cellule changée: (${r},${c}) ${currentGrid.getCell(r, c)} → ${newGrid.getCell(r, c)}`);
                                }
                            }
                        }
                    }
                    console.log(`[FLOODFILL] ${changedCells} cellules modifiées pour la coordonnée (${row},${col})`);

                    // Mettre à jour la grille courante pour la prochaine itération
                    currentGrid = newGrid;
                }
            }
        });
    }

    console.log(`[FLOODFILL] Total de ${totalChangedCells} cellules modifiées sur toutes les coordonnées`);

    console.log(`[FLOODFILL] Mise à jour de la grille via setGrid`);
    setGrid(currentGrid);
    console.log(`[FLOODFILL] Remplissage terminé avec succès`);
    return true;
}

async function executeUnifiedReplace(parameters: string | undefined, coordinates: string[], additionalCoords: string[], grid: Grid | null, setGrid: (g: Grid) => void, rawCommand?: string): Promise<boolean> {
    console.log(`[REPLACE] Début avec paramètres: ${parameters}, coordonnées: ${JSON.stringify(coordinates)}`);

    // Obtenir la grille la plus récente
    const { getCurrentGrid } = useGridStateStore.getState();
    const currentGrid = grid || getCurrentGrid();

    if (!currentGrid || !parameters) {
        console.error('[REPLACE] Paramètres manquants ou grille invalide');
        return false;
    }

    // Parser les paramètres: "from to" (ex: "1 2")
    const params = parameters.trim().split(/\s+/);
    if (params.length !== 2) {
        console.error(`[REPLACE] Format de paramètres invalide: ${parameters}. Attendu: "from to"`);
        return false;
    }

    const fromColors = params[0].split(',').map(str => parseInt(str.trim(), 10)).filter(n => !isNaN(n));
    const toColor = parseInt(params[1], 10);
    if (fromColors.length === 0 || isNaN(toColor)) {
        console.error(`[REPLACE] Couleurs invalides: ${params[0]} -> ${params[1]}`);
        return false;
    }

    // Utiliser l'extraction directe des blocs de coordonnées
    const coordinateBlocks = rawCommand ?
        extractCoordinateBlocksDirectly(rawCommand) :
        [];

    console.log(`[REPLACE] Remplacement couleurs ${fromColors.join(',')} -> ${toColor} dans ${coordinateBlocks.length} blocs`);

    const newGrid = currentGrid.clone();

    processCoordinateBlocks(coordinateBlocks, newGrid, (row1, col1, row2, col2) => {
        const minRow = Math.min(row1, row2);
        const maxRow = Math.max(row1, row2);
        const minCol = Math.min(col1, col2);
        const maxCol = Math.max(col1, col2);

        // Appliquer le remplacement sur cette région
        for (let r = minRow; r <= maxRow; r++) {
            for (let c = minCol; c <= maxCol; c++) {
                const currentColor = newGrid.getCell(r, c);
                if (currentColor !== null && currentColor !== undefined && fromColors.includes(currentColor)) {
                    newGrid.setCell(r, c, toColor);
                }
            }
        }
    });

    setGrid(newGrid);
    console.log(`[REPLACE] ✅ Remplacement terminé`);
    return true;
}

async function executeUnifiedCopy(coordinates: string[], _grid: Grid | null, rawCommand?: string): Promise<boolean> {
    console.log(`[COPY] Début avec coordonnées: ${JSON.stringify(coordinates)}, raw: ${rawCommand}`);

    // S'assurer d'avoir la grille la plus récente
    const { getCurrentGrid } = useGridStateStore.getState();
    const currentGrid = getCurrentGrid();

    if (!currentGrid || coordinates.length === 0) {
        console.error(`[COPY] Paramètres invalides - grid: ${!!currentGrid}, coords: ${coordinates.length}`);
        return false;
    }

    // Utiliser l'extraction directe des blocs de coordonnées si rawCommand est fourni
    const coordinateBlocks = rawCommand ?
        extractCoordinateBlocksDirectly(rawCommand) :
        // Fallback: traitement standard
        (() => {
            if (coordinates.length === 2) {
                return [[coordinates[0], coordinates[1]]];
            } else {
                return coordinates.map(coord => [coord]);
            }
        })();

    // Créer un Set de cellules sélectionnées en utilisant les blocs
    const selectedCells = new Set<string>();
    let minRow = Infinity, maxRow = -Infinity;
    let minCol = Infinity, maxCol = -Infinity;

    // Traitement unifié des blocs avec modificateurs
    processCoordinateBlocks(coordinateBlocks, currentGrid, (row1, col1, row2, col2) => {
        const minRowLocal = Math.min(row1, row2);
        const maxRowLocal = Math.max(row1, row2);
        const minColLocal = Math.min(col1, col2);
        const maxColLocal = Math.max(col1, col2);

        for (let r = minRowLocal; r <= maxRowLocal; r++) {
            for (let c = minColLocal; c <= maxColLocal; c++) {
                selectedCells.add(`${r},${c}`);
                minRow = Math.min(minRow, r);
                maxRow = Math.max(maxRow, r);
                minCol = Math.min(minCol, c);
                maxCol = Math.max(maxCol, c);
            }
        }
    });

    if (selectedCells.size === 0) {
        console.error(`[COPY] Aucune cellule sélectionnée après application des modificateurs`);
        return false;
    }

    console.log(`[COPY] Copie de ${selectedCells.size} cellules dans la zone: (${minRow},${minCol}) -> (${maxRow},${maxCol})`);

    // Utiliser extractClipboardDataFromGrid directement avec les cellules sélectionnées
    const { extractClipboardDataFromGrid } = await import('../utils/clipboardTransformUtils');
    const clipboardContentData = extractClipboardDataFromGrid(
        currentGrid,
        { minRow, maxRow, minCol, maxCol },
        selectedCells
    );

    // Mettre à jour le contenu du presse-papier directement
    const { content: _, ...clipboardState } = useClipboard.getState();
    useClipboard.setState({
        ...clipboardState,
        content: {
            data: clipboardContentData.data,
            mask: clipboardContentData.mask,
            width: clipboardContentData.width,
            height: clipboardContentData.height,
            sourceRow: minRow,
            sourceCol: minCol,
            isCut: false,
            timestamp: Date.now()
        },
        currentPosition: {
            row: minRow,
            col: minCol
        },
        hasTransferredSelection: true
    });
    console.log(`[COPY] Copie terminée avec succès - Contenu:`, useClipboard.getState().content);
    return true;
}

async function executeUnifiedCut(coordinates: string[], setGrid: (g: Grid) => void, rawCommand?: string): Promise<boolean> {
    console.log(`[CUT] Début avec coordonnées: ${JSON.stringify(coordinates)}, raw: ${rawCommand}`);

    // S'assurer d'avoir la grille la plus récente
    const { getCurrentGrid } = useGridStateStore.getState();
    const currentGrid = getCurrentGrid();

    if (!currentGrid || coordinates.length === 0) {
        console.error(`[CUT] Paramètres invalides - grid: ${!!currentGrid}, coords: ${coordinates.length}`);
        return false;
    }

    // Utiliser l'extraction directe des blocs de coordonnées si rawCommand est fourni
    const coordinateBlocks = rawCommand ?
        extractCoordinateBlocksDirectly(rawCommand) :
        // Fallback: traitement standard
        (() => {
            if (coordinates.length === 2) {
                return [[coordinates[0], coordinates[1]]];
            } else {
                return coordinates.map(coord => [coord]);
            }
        })();

    // Créer un Set de cellules sélectionnées en utilisant les blocs
    const selectedCells = new Set<string>();
    let minRow = Infinity, maxRow = -Infinity;
    let minCol = Infinity, maxCol = -Infinity;

    // Traitement unifié des blocs avec modificateurs
    processCoordinateBlocks(coordinateBlocks, currentGrid, (row1, col1, row2, col2) => {
        const minRowLocal = Math.min(row1, row2);
        const maxRowLocal = Math.max(row1, row2);
        const minColLocal = Math.min(col1, col2);
        const maxColLocal = Math.max(col1, col2);

        for (let r = minRowLocal; r <= maxRowLocal; r++) {
            for (let c = minColLocal; c <= maxColLocal; c++) {
                selectedCells.add(`${r},${c}`);
                minRow = Math.min(minRow, r);
                maxRow = Math.max(maxRow, r);
                minCol = Math.min(minCol, c);
                maxCol = Math.max(maxCol, c);
            }
        }
    });

    if (selectedCells.size === 0) {
        console.error(`[CUT] Aucune cellule sélectionnée après application des modificateurs`);
        return false;
    }

    console.log(`[CUT] Coupe de ${selectedCells.size} cellules dans la zone: (${minRow},${minCol}) -> (${maxRow},${maxCol})`);
    console.log(`[CUT] Grille utilisée: ${currentGrid.width}x${currentGrid.height}`);

    // Utiliser extractClipboardDataFromGrid directement avec les cellules sélectionnées
    const { extractClipboardDataFromGrid } = await import('../utils/clipboardTransformUtils');
    const clipboardContentData = extractClipboardDataFromGrid(
        currentGrid,
        { minRow, maxRow, minCol, maxCol },
        selectedCells
    );

    // Mettre à jour le contenu du presse-papier directement
    const { content: _, ...clipboardState } = useClipboard.getState();
    useClipboard.setState({
        ...clipboardState,
        content: {
            data: clipboardContentData.data,
            mask: clipboardContentData.mask,
            width: clipboardContentData.width,
            height: clipboardContentData.height,
            sourceRow: minRow,
            sourceCol: minCol,
            isCut: true,
            timestamp: Date.now()
        },
        currentPosition: {
            row: minRow,
            col: minCol
        },
        hasTransferredSelection: true
    });

    // Effacer les cellules sélectionnées de la grille
    const newGrid = currentGrid.clone();
    for (const cellKey of selectedCells) {
        const [row, col] = cellKey.split(',').map(Number);
        if (row >= 0 && row < newGrid.height && col >= 0 && col < newGrid.width) {
            newGrid.setCell(row, col, 0);
        }
    }
    setGrid(newGrid);

    // Vérifier que le presse-papier a bien été rempli
    const { content } = useClipboard.getState();
    console.log(`[CUT] ✅ Coupe terminée`, content?.data);
    return true;
}
async function executeUnifiedPaste(coordinates: string[], setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[PASTE] Début avec coordonnées: ${JSON.stringify(coordinates)}`);

    // S'assurer d'avoir la grille la plus récente
    const { getCurrentGrid } = useGridStateStore.getState();
    const currentGrid = getCurrentGrid();

    if (!currentGrid || coordinates.length === 0) {
        console.error(`[PASTE] Paramètres invalides - grid: ${!!currentGrid}, coords: ${coordinates.length}`);
        return false;
    }

    const [row, col] = coordinates[0].split(',').map(Number);
    if (isNaN(row) || isNaN(col)) {
        console.error(`[PASTE] Coordonnées invalides: ${row},${col}`);
        return false;
    }

    const { content } = useClipboard.getState();

    if (!content) {
        console.warn(`[PASTE] Aucun contenu dans le presse-papier`);
        return false;
    }

    console.log(`[PASTE] Contenu du presse-papier:`, {
        width: content.width,
        height: content.height,
        data: content.data,
        mask: content.mask,
        pastePosition: [row, col]
    });

    // Log détaillé du contenu
    console.log(`[PASTE] Données détaillées:`);
    for (let r = 0; r < content.height; r++) {
        console.log(`[PASTE] Ligne ${r}:`, content.data[r]);
    }
    console.log(`[PASTE] Masque détaillé:`);
    for (let r = 0; r < content.height; r++) {
        console.log(`[PASTE] Masque ligne ${r}:`, content.mask ? content.mask[r] : 'pas de masque');
    }

    // Cloner la grille et appliquer le contenu directement
    const newGrid = currentGrid.clone();

    // Appliquer le contenu du presse-papier directement
    let cellsModified = 0;
    let cellsProcessed = 0; // Compteur de cellules traitées avec succès
    for (let r = 0; r < content.height; r++) {
        for (let c = 0; c < content.width; c++) {
            const gridRow = row + r;
            const gridCol = col + c;
            const valueToPaste = content.data[r][c];
            const shouldPaste = content.mask ? content.mask[r][c] : true;

            console.log(`[PASTE] Position (${r},${c}) -> grille (${gridRow},${gridCol}), valeur: ${valueToPaste}, shouldPaste: ${shouldPaste}`);

            if (
                gridRow >= 0 &&
                gridRow < newGrid.height &&
                gridCol >= 0 &&
                gridCol < newGrid.width
            ) {
                if (shouldPaste && valueToPaste !== -1) {
                    const oldValue = newGrid.getCell(gridRow, gridCol);
                    newGrid.setCell(gridRow, gridCol, valueToPaste);
                    cellsProcessed++; // Cellule traitée avec succès
                    if (oldValue !== valueToPaste) {
                        cellsModified++;
                        //console.log(`[PASTE] Cellule modifiée [${gridRow},${gridCol}]: ${oldValue} -> ${valueToPaste}`);
                    } else {
                        //console.log(`[PASTE] Cellule [${gridRow},${gridCol}] inchangée: ${oldValue} = ${valueToPaste}`);
                    }
                } else {
                    console.log(`[PASTE] Cellule [${gridRow},${gridCol}] ignorée: shouldPaste=${shouldPaste}, valueToPaste=${valueToPaste}`);
                }
            } else {
                //console.log(`[PASTE] Position [${gridRow},${gridCol}] hors limites de la grille ${newGrid.height}x${newGrid.width}`);
            }
        }
    }

    console.log(`[PASTE] Nombre de cellules modifiées: ${cellsModified}`);
    console.log(`[PASTE] Nombre de cellules traitées: ${cellsProcessed}`);
    // Application du contenu terminée

    // Vérifier que la grille a réellement changé
    let hasChanged = false;
    for (let r = 0; r < Math.min(currentGrid.height, newGrid.height); r++) {
        for (let c = 0; c < Math.min(currentGrid.width, newGrid.width); c++) {
            if (currentGrid.getCell(r, c) !== newGrid.getCell(r, c)) {
                hasChanged = true;
                break;
            }
        }
        if (hasChanged) break;
    }

    console.log(`[PASTE] La grille a-t-elle changé ? ${hasChanged}`);

    // Le collage réussit si au moins une cellule a été traitée avec succès
    // même si aucune valeur n'a changé (ex: coller 0 sur 0)
    if (cellsProcessed > 0) {
        if (hasChanged) {
            setGrid(newGrid);
            console.log(`[PASTE] Collage terminé avec succès, grille mise à jour`);
        } else {
            console.log(`[PASTE] Collage terminé avec succès, aucune modification visuelle`);
        }
        return true;
    } else {
        console.error(`[PASTE] Aucune cellule traitée, collage échoué`);
        return false;
    }
}

// async function executeSelectInvert(coordinates: string[], grid: Grid | null): Promise<boolean> {
//     if (!grid || coordinates.length < 2) return false;
//     const [startRow, startCol] = coordinates[0].split(',').map(Number);
//     const [endRow, endCol] = coordinates[1].split(',').map(Number);
//     if ([startRow, startCol, endRow, endCol].some(isNaN)) return false;

//     const { selectedCells, selectCell } = useSelection.getState();
//     const minRow = Math.min(startRow, endRow);
//     const maxRow = Math.max(startRow, endRow);
//     const minCol = Math.min(startCol, endCol);
//     const maxCol = Math.max(startCol, endCol);

//     for (let r = minRow; r <= maxRow; r++) {
//         for (let c = minCol; c <= maxCol; c++) {
//             const cellKey = `${r},${c}`;
//             selectCell(r, c, selectedCells.has(cellKey) ? 'subtract' : 'add');
//         }
//     }
//     return true;
// }

// async function executeSelectColor(colorParams: string | undefined, coordinates: string[], grid: Grid | null): Promise<boolean> {
//     if (!grid || !colorParams || coordinates.length < 2) return false;
//     const colors = new Set(colorParams.split(',').map(Number).filter(c => !isNaN(c)));
//     if (colors.size === 0) return false;

//     const [startRow, startCol] = coordinates[0].split(',').map(Number);
//     const [endRow, endCol] = coordinates[1].split(',').map(Number);
//     if ([startRow, startCol, endRow, endCol].some(isNaN)) return false;

//     const { clearSelection, selectCell } = useSelection.getState();
//     clearSelection();

//     const minRow = Math.min(startRow, endRow);
//     const maxRow = Math.max(startRow, endRow);
//     const minCol = Math.min(startCol, endCol);
//     const maxCol = Math.max(startCol, endCol);

//     for (let r = minRow; r <= maxRow; r++) {
//         for (let c = minCol; c <= maxCol; c++) {
//             const cellColor = grid.getCell(r, c) ?? -1;
//             // Ne sélectionner que les cellules non vides (couleur non nulle) et dont la couleur est dans l'ensemble
//             if (cellColor !== 0 && colors.has(cellColor)) {
//                 selectCell(r, c, 'add');
//             }
//         }
//     }
//     return true;
// }

// --- Nouvelles fonctions pour commandes unifiées ---



async function executeUnifiedResize(parameters: string | undefined, _grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    try {
        if (!parameters) {
            console.error('RESIZE: paramètres manquants');
            return false;
        }

        console.log(`[RESIZE] Redimensionnement vers: ${parameters}`);

        // Parser les dimensions : "9x9"
        const match = parameters.match(/(\d+)x(\d+)/);
        if (!match) {
            console.error('RESIZE: format invalide, attendu NxM');
            return false;
        }

        const newWidth = parseInt(match[1]);
        const newHeight = parseInt(match[2]);

        // Obtenir la grille la plus récente
        const { getCurrentGrid } = useGridStateStore.getState();
        const grid = getCurrentGrid();

        if (grid) {
            // Créer une nouvelle grille avec les dimensions spécifiées
            const newGrid = new Grid(newHeight, newWidth);

            // Copier le contenu existant dans les limites
            for (let r = 0; r < Math.min(grid.height, newHeight); r++) {
                for (let c = 0; c < Math.min(grid.width, newWidth); c++) {
                    const cell = grid.getCell(r, c);
                    if (cell !== null && cell !== undefined) {
                        newGrid.setCell(r, c, cell);
                    }
                }
            }

            setGrid(newGrid);
        }

        return true;
    } catch (error) {
        console.error('Erreur executeUnifiedResize:', error);
        return false;
    }
}



async function executeUnifiedEnd(): Promise<boolean> {
    try {
        console.log(`[END] Fin du scénario - proposition et validation de la grille`);

        // Libérer la sélection et le presse-papier au début de END
        console.log(`[END] Libération de la sélection et du presse-papier`);
        const { clearSelection } = useSelection.getState();
        clearSelection();
        const { clear } = useClipboard.getState();
        clear();
        console.log(`[END] Sélections et presse-papier libérés`);

        // 1. Obtenir la grille actuelle
        const { getCurrentGrid } = useGridStateStore.getState();
        const currentGrid = getCurrentGrid();

        if (!currentGrid) {
            console.error('[END] Aucune grille disponible pour la proposition');
            return false;
        }

        // 2. Sauvegarder dans l'historique avant de proposer (comme dans handleProposeClick)
        const gridStateStore = useGridStateStore.getState();
        const { useGridHistoryStore } = await import('./useGridHistoryStore');
        const gridHistoryStore = useGridHistoryStore.getState();
        gridHistoryStore.saveGridOnlyHistory(gridStateStore.currentTestIndex, currentGrid);

        // 3. Proposer la grille (comme le bouton "Proposer cette grille")
        const { proposeCurrentGrid } = useGridStateStore.getState();
        proposeCurrentGrid();
        console.log(`[END] Grille proposée avec succès`);

        // 4. Attendre un moment pour que la proposition soit traitée
        await new Promise(resolve => setTimeout(resolve, 100));

        // 5. Valider la proposition (comme le bouton "Valider la Proposition")
        // Utiliser l'événement existant 'validate-from-automation'
        const event = new CustomEvent('validate-from-automation');
        window.dispatchEvent(event);
        console.log(`[END] Demande de validation envoyée`);

        console.log(`[END] Fin du scénario terminée - grille proposée et validation demandée`);
        return true;
    } catch (error) {
        console.error('Erreur executeUnifiedEnd:', error);
        return false;
    }
}

async function executeUnifiedSurround(parameters: string | undefined, coordinates: string[], additionalCoords: string[], grid: Grid | null, setGrid: (g: Grid) => void, rawCommand?: string): Promise<boolean> {
    if (!grid || !parameters) return false;

    const color = parseInt(parameters);
    if (isNaN(color)) return false;

    const newGrid = grid.clone();
    let modified = false;

    // Utiliser l'extraction directe des blocs de coordonnées
    const coordinateBlocks = rawCommand ?
        extractCoordinateBlocksDirectly(rawCommand) :
        // Fallback: traiter toutes les coordonnées comme des cellules individuelles
        [...coordinates, ...additionalCoords].map(coord => [coord]);

    console.log(`[SURROUND] Traitement de ${coordinateBlocks.length} blocs de coordonnées:`, coordinateBlocks);

    // Traiter chaque bloc (généralement des cellules individuelles pour SURROUND)
    processCoordinateBlocks(coordinateBlocks, newGrid, (row1, col1, row2, col2) => {
        // Pour SURROUND, traiter chaque cellule dans le rectangle (si c'est un rectangle)
        const minRow = Math.min(row1, row2);
        const maxRow = Math.max(row1, row2);
        const minCol = Math.min(col1, col2);
        const maxCol = Math.max(col1, col2);

        for (let centerRow = minRow; centerRow <= maxRow; centerRow++) {
            for (let centerCol = minCol; centerCol <= maxCol; centerCol++) {
                // Entourer chaque cellule
                for (let r = centerRow - 1; r <= centerRow + 1; r++) {
                    for (let c = centerCol - 1; c <= centerCol + 1; c++) {
                        // Exclure la cellule centrale
                        if (r === centerRow && c === centerCol) continue;

                        if (r >= 0 && r < newGrid.height && c >= 0 && c < newGrid.width) {
                            newGrid.setCell(r, c, color);
                            modified = true;
                        }
                    }
                }
            }
        }
    });

    if (modified) {
        setGrid(newGrid);
    }

    return true;
}

/**
 * Exécute l'opération XOR binaire entre deux zones
 */
async function executeUnifiedXor(parameters: string | undefined, grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[XOR] Début avec paramètres: ${parameters}`);

    if (!grid || !parameters) return false;

    try {
        // Parser les coordonnées directement depuis les paramètres
        // Format: ([0,0 2,2] [0,4 2,6]) -> extraire [0,0 2,2] et [0,4 2,6]
        const coordPattern = /\[([^\]]+)\]/g;
        const coordMatches = [];
        let match;
        while ((match = coordPattern.exec(parameters)) !== null) {
            coordMatches.push(`[${match[1]}]`);
        }

        console.log(`[XOR] coordonnées extraites:`, coordMatches);
        if (coordMatches.length !== 2) {
            console.warn(`[XOR] Nécessite exactement 2 zones, trouvées: ${coordMatches.length}`);
            return false;
        }

        // Reproduire exactement la logique interactive : opération cellule par cellule
        const newGrid = grid.clone();

        // Parser les rectangles depuis les coordonnées
        const rectangles = coordMatches.map(coordMatch => {
            const coords = extractCoordsFromOperand(coordMatch);
            const coordPairs = coords.map(coord => coord.split(',').map(Number));
            const rows = coordPairs.map(([row]) => row);
            const cols = coordPairs.map(([, col]) => col);
            return {
                startRow: Math.min(...rows),
                startCol: Math.min(...cols),
                endRow: Math.max(...rows),
                endCol: Math.max(...cols)
            };
        });

        console.log(`[XOR] rectangles parsés:`, rectangles);

        // Vérifier que toutes les zones ont la même taille (comme dans l'interactive)
        const firstRect = rectangles[0];
        const width = firstRect.endCol - firstRect.startCol + 1;
        const height = firstRect.endRow - firstRect.startRow + 1;

        for (let i = 1; i < rectangles.length; i++) {
            const rect = rectangles[i];
            const rectWidth = rect.endCol - rect.startCol + 1;
            const rectHeight = rect.endRow - rect.startRow + 1;
            if (rectWidth !== width || rectHeight !== height) {
                console.warn(`[XOR] Toutes les zones doivent avoir la même taille`);
                return false;
            }
        }

        console.log(`[XOR] Traitement zone ${width}x${height}`);

        // Appliquer l'opération cellule par cellule (comme dans l'interactive)
        for (let row = 0; row < height; row++) {
            for (let col = 0; col < width; col++) {
                const firstZoneRow = firstRect.startRow + row;
                const firstZoneCol = firstRect.startCol + col;

                // Obtenir la valeur de la première zone
                let resultValue = grid.getCell(firstZoneRow, firstZoneCol) ?? 0;

                // Appliquer l'opération avec toutes les autres zones
                for (let i = 1; i < rectangles.length; i++) {
                    const otherRect = rectangles[i];
                    const otherZoneRow = otherRect.startRow + row;
                    const otherZoneCol = otherRect.startCol + col;
                    const otherValue = grid.getCell(otherZoneRow, otherZoneCol) ?? 0;

                    // Appliquer l'opération binaire XOR (0=fond, 1-9=valeur)
                    // XOR: résultat = 0 si les deux cellules ont le même état (toutes deux 0 ou toutes deux non-0)
                    //      résultat = valeur non-nulle si exactement une des cellules est non-nulle
                    const val1Binary = resultValue === 0 ? 0 : 1;
                    const val2Binary = otherValue === 0 ? 0 : 1;
                    const binaryResult = val1Binary ^ val2Binary; // XOR binaire

                    if (binaryResult === 0) {
                        resultValue = 0; // Même état -> résultat 0
                    } else {
                        // États différents -> garder la valeur non-nulle
                        resultValue = resultValue !== 0 ? resultValue : otherValue;
                    }
                }

                // Écrire le résultat dans la première zone
                newGrid.setCell(firstZoneRow, firstZoneCol, resultValue);
            }
        }

        setGrid(newGrid);
        console.log(`[XOR] ✅ Opération terminée`);
        return true;

    } catch (error) {
        console.error(`[XOR] Erreur:`, error);
        return false;
    }
}

/**
 * Exécute l'opération AND binaire entre plusieurs zones
 */
async function executeUnifiedAnd(parameters: string | undefined, grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[AND] Début avec paramètres: ${parameters}`);

    if (!grid || !parameters) return false;

    try {
        // Parser les coordonnées directement depuis les paramètres
        // Format: ([0,0 2,2] [0,4 2,6]) -> extraire [0,0 2,2] et [0,4 2,6]
        const coordPattern = /\[([^\]]+)\]/g;
        const coordMatches = [];
        let match;
        while ((match = coordPattern.exec(parameters)) !== null) {
            coordMatches.push(`[${match[1]}]`);
        }

        console.log(`[AND] coordonnées extraites:`, coordMatches);
        if (coordMatches.length < 2) {
            console.warn(`[AND] Nécessite au moins 2 zones, trouvées: ${coordMatches.length}`);
            return false;
        }

        // Reproduire exactement la logique interactive : opération cellule par cellule
        const newGrid = grid.clone();

        // Parser les rectangles depuis les coordonnées
        const rectangles = coordMatches.map(coordMatch => {
            const coords = extractCoordsFromOperand(coordMatch);
            const coordPairs = coords.map(coord => coord.split(',').map(Number));
            const rows = coordPairs.map(([row]) => row);
            const cols = coordPairs.map(([, col]) => col);
            return {
                startRow: Math.min(...rows),
                startCol: Math.min(...cols),
                endRow: Math.max(...rows),
                endCol: Math.max(...cols)
            };
        });

        console.log(`[AND] rectangles parsés:`, rectangles);

        // Vérifier que toutes les zones ont la même taille (comme dans l'interactive)
        const firstRect = rectangles[0];
        const width = firstRect.endCol - firstRect.startCol + 1;
        const height = firstRect.endRow - firstRect.startRow + 1;

        for (let i = 1; i < rectangles.length; i++) {
            const rect = rectangles[i];
            const rectWidth = rect.endCol - rect.startCol + 1;
            const rectHeight = rect.endRow - rect.startRow + 1;
            if (rectWidth !== width || rectHeight !== height) {
                console.warn(`[AND] Toutes les zones doivent avoir la même taille`);
                return false;
            }
        }

        console.log(`[AND] Traitement zone ${width}x${height}`);

        // Appliquer l'opération cellule par cellule (comme dans l'interactive)
        for (let row = 0; row < height; row++) {
            for (let col = 0; col < width; col++) {
                const firstZoneRow = firstRect.startRow + row;
                const firstZoneCol = firstRect.startCol + col;

                // Obtenir la valeur de la première zone
                let resultValue = grid.getCell(firstZoneRow, firstZoneCol) ?? 0;

                // Appliquer l'opération avec toutes les autres zones
                for (let i = 1; i < rectangles.length; i++) {
                    const otherRect = rectangles[i];
                    const otherZoneRow = otherRect.startRow + row;
                    const otherZoneCol = otherRect.startCol + col;
                    const otherValue = grid.getCell(otherZoneRow, otherZoneCol) ?? 0;

                    // Appliquer l'opération binaire AND (0=fond, 1-9=valeur)
                    // AND: résultat = 0 si au moins une cellule est 0, sinon préserver la première valeur non-nulle
                    if (resultValue === 0 || otherValue === 0) {
                        resultValue = 0; // Si l'une des cellules est 0, le résultat est 0
                    }
                    // Si les deux sont non-nulles, on garde resultValue (première valeur non-nulle)
                }

                // Écrire le résultat dans la première zone
                newGrid.setCell(firstZoneRow, firstZoneCol, resultValue);
            }
        }

        setGrid(newGrid);
        console.log(`[AND] ✅ Opération terminée`);
        return true;

    } catch (error) {
        console.error(`[AND] Erreur:`, error);
        return false;
    }
}

/**
 * Exécute l'opération OR binaire entre plusieurs zones
 */
async function executeUnifiedOr(parameters: string | undefined, grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    console.log(`[OR] Début avec paramètres: ${parameters}`);

    if (!grid || !parameters) return false;

    try {
        // Parser les coordonnées directement depuis les paramètres
        // Format: ([0,0 2,2] [0,4 2,6]) -> extraire [0,0 2,2] et [0,4 2,6]
        const coordPattern = /\[([^\]]+)\]/g;
        const coordMatches = [];
        let match;
        while ((match = coordPattern.exec(parameters)) !== null) {
            coordMatches.push(`[${match[1]}]`);
        }

        console.log(`[OR] coordonnées extraites:`, coordMatches);
        if (coordMatches.length < 2) {
            console.warn(`[OR] Nécessite au moins 2 zones, trouvées: ${coordMatches.length}`);
            return false;
        }

        // Reproduire exactement la logique interactive : opération cellule par cellule
        const newGrid = grid.clone();

        // Parser les rectangles depuis les coordonnées
        const rectangles = coordMatches.map(coordMatch => {
            const coords = extractCoordsFromOperand(coordMatch);
            const coordPairs = coords.map(coord => coord.split(',').map(Number));
            const rows = coordPairs.map(([row]) => row);
            const cols = coordPairs.map(([, col]) => col);
            return {
                startRow: Math.min(...rows),
                startCol: Math.min(...cols),
                endRow: Math.max(...rows),
                endCol: Math.max(...cols)
            };
        });

        console.log(`[OR] rectangles parsés:`, rectangles);

        // Vérifier que toutes les zones ont la même taille (comme dans l'interactive)
        const firstRect = rectangles[0];
        const width = firstRect.endCol - firstRect.startCol + 1;
        const height = firstRect.endRow - firstRect.startRow + 1;

        for (let i = 1; i < rectangles.length; i++) {
            const rect = rectangles[i];
            const rectWidth = rect.endCol - rect.startCol + 1;
            const rectHeight = rect.endRow - rect.startRow + 1;
            if (rectWidth !== width || rectHeight !== height) {
                console.warn(`[OR] Toutes les zones doivent avoir la même taille`);
                return false;
            }
        }

        console.log(`[OR] Traitement zone ${width}x${height}`);

        // Appliquer l'opération cellule par cellule (comme dans l'interactive)
        for (let row = 0; row < height; row++) {
            for (let col = 0; col < width; col++) {
                const firstZoneRow = firstRect.startRow + row;
                const firstZoneCol = firstRect.startCol + col;

                // Obtenir la valeur de la première zone
                let resultValue = grid.getCell(firstZoneRow, firstZoneCol) ?? 0;

                // Appliquer l'opération avec toutes les autres zones
                for (let i = 1; i < rectangles.length; i++) {
                    const otherRect = rectangles[i];
                    const otherZoneRow = otherRect.startRow + row;
                    const otherZoneCol = otherRect.startCol + col;
                    const otherValue = grid.getCell(otherZoneRow, otherZoneCol) ?? 0;

                    // Appliquer l'opération binaire OR (0=fond, 1-9=valeur)
                    // OR: résultat = 0 seulement si toutes les cellules sont 0, sinon prendre la première valeur non-nulle
                    if (resultValue === 0 && otherValue !== 0) {
                        resultValue = otherValue; // Si resultValue est 0 mais otherValue non-nul, prendre otherValue
                    }
                    // Si resultValue est déjà non-nul, on le garde (première valeur non-nulle trouvée)
                }

                // Écrire le résultat dans la première zone
                newGrid.setCell(firstZoneRow, firstZoneCol, resultValue);
            }
        }

        setGrid(newGrid);
        console.log(`[OR] ✅ Opération terminée`);
        return true;

    } catch (error) {
        console.error(`[OR] Erreur:`, error);
        return false;
    }
}

// Removed unused functions getZoneHomogeneousValue and getZoneHomogeneousValueFromCoord

async function executeStandardUnifiedCommand(command: UnifiedPlaybackCommand, grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    const { action, parameters, coordinates, additionalCoordinates = [] } = command;
    console.log(`[executeStandardUnifiedCommand] Exécution de la commande standard: ${action} avec paramètres: ${parameters}, coordonnées: ${JSON.stringify(coordinates)}, additionalCoords: ${JSON.stringify(additionalCoordinates)}`);

    switch (action) {
        case 'INIT':
            return await executeUnifiedInit(parameters, grid, setGrid);
        case 'FILL':
            return await executeUnifiedFill(parameters, coordinates, additionalCoordinates, grid, setGrid, command.raw);
        case 'CLEAR':
            return await executeUnifiedClear(coordinates, additionalCoordinates, grid, setGrid, command.raw);
        case 'SELECT':
            return await executeUnifiedSelect(coordinates, additionalCoordinates);
        case 'EDIT':
            return await executeUnifiedEdit(parameters, coordinates, grid, setGrid);
        case 'INSERT':
            return await executeUnifiedInsert(parameters, coordinates, grid, setGrid);
        case 'DELETE':
            return await executeUnifiedDelete(parameters, coordinates, grid, setGrid);
        case 'EXTRACT':
            return await executeUnifiedExtract(coordinates, grid, setGrid);
        case 'MULTIPLY':
            return await executeUnifiedMultiply(parameters, coordinates, grid, setGrid);
        case 'DIVIDE':
            return await executeUnifiedDivide(parameters, coordinates, grid, setGrid);
        case 'COPY':
            return await executeUnifiedCopy(coordinates, grid, command.raw);
        case 'CUT':
            return await executeUnifiedCut(coordinates, setGrid, command.raw);
        case 'PASTE':
            return await executeUnifiedPaste(coordinates, setGrid);
        case 'FLIP':
            return await executeUnifiedFlip(parameters, coordinates, grid, setGrid);
        case 'ROTATE':
            return await executeUnifiedRotate(parameters, coordinates, grid, setGrid);
        case 'FLOODFILL':
            return await executeUnifiedFloodFill(parameters, coordinates, additionalCoordinates, grid, setGrid, command.raw);
        case 'REPLACE':
            return await executeUnifiedReplace(parameters, coordinates, additionalCoordinates, grid, setGrid, command.raw);
        case 'RESIZE':
            return await executeUnifiedResize(parameters, grid, setGrid);
        case 'SURROUND':
            return await executeUnifiedSurround(parameters, coordinates, additionalCoordinates, grid, setGrid, command.raw);

        case 'XOR':
            return await executeUnifiedXor(parameters, grid, setGrid);
        case 'AND':
            return await executeUnifiedAnd(parameters, grid, setGrid);
        case 'OR':
            return await executeUnifiedOr(parameters, grid, setGrid);

        case 'END':
            return await executeUnifiedEnd();
        default:
            console.warn(`Action standard non implémentée: ${action}`);
            return false;
    }
}

/**
 * Fonction utilitaire principale pour exécuter une commande unifiée.
 * EXPORTÉE pour être utilisée par le hook de lecture.
 */
export async function executeUnifiedCommand(command: UnifiedPlaybackCommand, grid: Grid | null, setGrid: (grid: Grid) => void): Promise<boolean> {
    try {
        // Vérifier si c'est une commande groupée qui doit être décomposée
        const groupedCommands = [
            'EDITS',        // EDIT consécutifs
            'FILLS',        // FILL consécutifs
            'FLOODFILLS',   // FLOODFILL consécutifs
            'REPLACES',     // REPLACE consécutifs
            'CLEARS',       // CLEAR consécutifs
            'SURROUNDS',    // SURROUND consécutifs
            'FLIPS',        // FLIP consécutifs
            'ROTATES',      // ROTATE consécutifs
            'INSERTS',      // INSERT consécutifs
            'DELETES',      // DELETE consécutifs
            'RESIZES',      // RESIZE consécutifs
            'TRANSFERT',    // Commandes de transfert groupées
            'MOTIF'         // Commandes de motif groupées
        ];
        if (groupedCommands.includes(command.action)) {
            console.log(`[executeUnifiedCommand] Décomposition de la commande groupée: ${command.action}`);
            return await executeGroupedCommand(command, grid, setGrid);
        }

        switch (command.executionType) {
            case 'standard':
                return await executeStandardUnifiedCommand(command, grid, setGrid);
            case 'motif':
                return await executeMotifCommand(command, grid, setGrid);
            case 'special_selection':
                console.error(`[executeUnifiedCommand] Erreur lors de l'exécution de ${command.action}:`, command.executionType);
                return false;
            // return await executeSpecialSelectionCommand(command, grid, setGrid);
            default:
                console.warn(`Type d'exécution non supporté: ${command.executionType}`);
                return false;
        }
    } catch (error) {
        console.error(`[executeUnifiedCommand] Erreur lors de l'exécution de ${command.action}:`, error);
        return false;
    }
}

async function executeMotifCommand(command: UnifiedPlaybackCommand, grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    const { action, parameters, coordinates } = command;
    console.log(`[executeMotifCommand] Exécution de la commande motif: ${action} avec paramètres: ${parameters}, coordonnées: ${JSON.stringify(coordinates)}`);

    switch (action) {
        case 'FLIP':
            return await executeUnifiedFlip(parameters, coordinates, grid, setGrid);
        case 'ROTATE':
            return await executeUnifiedRotate(parameters, coordinates, grid, setGrid);
        case 'COPY':
            return await executeUnifiedCopy(coordinates, grid, command.raw);
        case 'CUT':
            return await executeUnifiedCut(coordinates, setGrid, command.raw);
        case 'PASTE':
            return await executeUnifiedPaste(coordinates, setGrid);
        case 'FILL':
            return await executeUnifiedFill(parameters, coordinates, [], grid, setGrid);
        default:
            console.warn(`Action motif non implémentée: ${action}`);
            return false;
    }
}


/**
 * Décompose et exécute une commande groupée (EDITS, FILLS, REPLACES, etc. plus TRANSFFERT et MOTIF)
 */
async function executeGroupedCommand(command: UnifiedPlaybackCommand, grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    try {
        if (!command.parameters) {
            console.warn(`[${command.action}] Aucun paramètre fourni pour la commande groupée`);
            return false;
        }

        if (!grid) {
            console.warn(`[${command.action}] Aucune grille disponible`);
            return false;
        }

        // Utiliser les subcommands si elles existent (commandes déjà parsées)
        // Sinon, décomposer les paramètres (fallback pour compatibilité)
        let individualCommands: string[];
        if (command.subcommands && command.subcommands.length > 0) {
            // Utiliser les commandes déjà parsées
            individualCommands = command.subcommands.map(subcmd => subcmd.raw || '');
        } else {
            // Fallback: parser les paramètres
            // Retirer les accolades si présentes
            let cleanParameters = command.parameters;
            if (cleanParameters.startsWith('{') && cleanParameters.endsWith('}')) {
                cleanParameters = cleanParameters.slice(1, -1);
            }
            individualCommands = cleanParameters.split(';').map(cmd => cmd.trim()).filter(cmd => cmd.length > 0);
        }

        console.log(`[${command.action}] Exécution de ${individualCommands.length} commandes`);

        let currentGrid = grid;
        let successCount = 0;

        // Pour TRANSFERT, exécuter toutes les commandes sans mise à jour visuelle intermédiaire
        if (command.action === 'TRANSFERT') {
            // Créer une fonction setGrid temporaire qui ne fait rien
            const tempSetGrid = (newGrid: Grid) => {
                currentGrid = newGrid;
                // Ne pas appeler setGrid pour éviter l'affichage progressif
            };

            // Exécuter chaque commande individuelle sans mise à jour visuelle
            for (let i = 0; i < individualCommands.length; i++) {
                let parsedIndividualCommand;

                if (command.subcommands && command.subcommands.length > 0) {
                    // Utiliser la commande déjà parsée
                    parsedIndividualCommand = command.subcommands[i];
                } else {
                    // Fallback: parser la commande individuelle
                    const individualCommand = individualCommands[i];
                    const { parseUnifiedCommandForPlayback } = await import('../utils/commandGenerationUtils');
                    parsedIndividualCommand = await parseUnifiedCommandForPlayback(individualCommand);
                }

                if (parsedIndividualCommand.action === 'UNKNOWN') {
                    continue;
                }

                // Exécuter la commande individuelle avec la fonction setGrid temporaire
                const success = await executeStandardUnifiedCommand(parsedIndividualCommand, currentGrid, tempSetGrid);

                if (success) {
                    successCount++;
                }
            }

            // Mettre à jour la grille une seule fois à la fin pour TRANSFERT
            if (successCount > 0) {
                setGrid(currentGrid);
            }
        } else {
            // Pour les autres commandes groupées, garder le comportement original
            for (let i = 0; i < individualCommands.length; i++) {
                let parsedIndividualCommand;

                if (command.subcommands && command.subcommands.length > 0) {
                    // Utiliser la commande déjà parsée
                    parsedIndividualCommand = command.subcommands[i];
                    console.log(`[${command.action}] Exécution ${i + 1}/${individualCommands.length}: ${parsedIndividualCommand.raw || parsedIndividualCommand.action}`);
                } else {
                    // Fallback: parser la commande individuelle
                    const individualCommand = individualCommands[i];
                    console.log(`[${command.action}] Exécution ${i + 1}/${individualCommands.length}: ${individualCommand}`);
                    const { parseUnifiedCommandForPlayback } = await import('../utils/commandGenerationUtils');
                    parsedIndividualCommand = await parseUnifiedCommandForPlayback(individualCommand);
                }

                if (parsedIndividualCommand.action === 'UNKNOWN') {
                    console.warn(`[${command.action}] Commande individuelle non reconnue: ${parsedIndividualCommand.raw || 'unknown'}`);
                    continue;
                }

                // Exécuter la commande individuelle avec mise à jour visuelle
                const success = await executeStandardUnifiedCommand(parsedIndividualCommand, currentGrid, (newGrid) => {
                    currentGrid = newGrid;
                    setGrid(newGrid);
                });

                if (success) {
                    successCount++;
                } else {
                    console.warn(`[${command.action}] Échec commande ${i + 1}: ${parsedIndividualCommand.raw || parsedIndividualCommand.action}`);
                }
            }
        }

        console.log(`[${command.action}] ✅ ${successCount}/${individualCommands.length} commandes exécutées`);
        return successCount > 0;

    } catch (error) {
        console.error(`Erreur executeGroupedCommand pour ${command.action}:`, error);
        return false;
    }
}

// Nouvelle fonction pour gérer les commandes EDIT individuelles
async function executeUnifiedEdit(colorParam: string | undefined, coordinates: string[], grid: Grid | null, setGrid: (g: Grid) => void): Promise<boolean> {
    // Utiliser la grille passée en paramètre ou obtenir la grille actuelle
    const currentGrid = grid || useGridStateStore.getState().getCurrentGrid();

    if (!currentGrid || colorParam === undefined) return false;
    try {
        const color = parseInt(colorParam, 10);
        if (isNaN(color)) return false;

        // Pour EDIT, on traite toutes les coordonnées (pas d'additionalCoords car EDIT ne prend qu'une seule coordonnée)
        const positions = coordinates.map(coord => {
            const [row, col] = coord.split(',').map(Number);
            return { row, col };
        });

        const newGrid = currentGrid.clone();
        let modificationsCount = 0;

        positions.forEach(pos => {
            if (!isNaN(pos.row) && !isNaN(pos.col) && pos.row >= 0 && pos.row < newGrid.height && pos.col >= 0 && pos.col < newGrid.width) {
                newGrid.setCell(pos.row, pos.col, color);
                modificationsCount++;
            }
        });

        if (modificationsCount > 0) {
            setGrid(newGrid);
            return true;
        } else {
            return false;
        }
    } catch (error) {
        console.error('Erreur executeUnifiedEdit:', error);
        return false;
    }
}

// ==================================================================
// FONCTIONS DE RÉSOLUTION DES OPÉRATEURS XOR, AND, OR
// ==================================================================

/**
 * Résout l'opérateur XOR entre deux sélections
 * XOR retourne les cellules qui sont dans l'une OU l'autre sélection, mais pas dans les deux
 */
function resolveXorOperator(operand1: string, operand2: string): string[] {
    const coords1 = extractCoordsFromOperand(operand1);
    const coords2 = extractCoordsFromOperand(operand2);

    const set1 = new Set(coords1);
    const set2 = new Set(coords2);

    const result: string[] = [];

    // Ajouter les cellules qui sont dans set1 mais pas dans set2
    set1.forEach(coord => {
        if (!set2.has(coord)) {
            result.push(coord);
        }
    });

    // Ajouter les cellules qui sont dans set2 mais pas dans set1
    set2.forEach(coord => {
        if (!set1.has(coord)) {
            result.push(coord);
        }
    });

    console.log(`[XOR] Résolution: ${coords1.length} + ${coords2.length} -> ${result.length} cellules`);
    return result;
}

/**
 * Résout l'opérateur AND entre plusieurs sélections
 * AND retourne les cellules qui sont présentes dans TOUTES les sélections
 */
function resolveAndOperator(operands: string[]): string[] {
    console.log(`[resolveAndOperator] Début avec operands:`, operands);
    if (operands.length < 2) return [];

    const coordSets = operands.map((operand, index) => {
        const coords = extractCoordsFromOperand(operand);
        console.log(`[resolveAndOperator] Operand ${index} (${operand}) -> coords:`, coords);
        return new Set(coords);
    });

    console.log(`[resolveAndOperator] coordSets créés:`, coordSets.map(set => Array.from(set)));

    // Commencer avec le premier ensemble
    let result = coordSets[0];
    console.log(`[resolveAndOperator] Ensemble initial:`, Array.from(result));

    // Faire l'intersection avec tous les autres ensembles
    for (let i = 1; i < coordSets.length; i++) {
        const intersection = new Set<string>();
        result.forEach(coord => {
            if (coordSets[i].has(coord)) {
                intersection.add(coord);
            }
        });
        console.log(`[resolveAndOperator] Intersection avec ensemble ${i}:`, Array.from(intersection));
        result = intersection;
    }

    const resultArray = Array.from(result);
    console.log(`[AND] Résolution de ${operands.length} opérandes -> ${resultArray.length} cellules`);
    return resultArray;
}

/**
 * Résout l'opérateur OR entre plusieurs sélections
 * OR retourne les cellules qui sont présentes dans AU MOINS UNE des sélections
 */
function resolveOrOperator(operands: string[]): string[] {
    const allCoords = new Set<string>();

    operands.forEach(operand => {
        const coords = extractCoordsFromOperand(operand);
        coords.forEach(coord => allCoords.add(coord));
    });

    const result = Array.from(allCoords);
    console.log(`[OR] Résolution de ${operands.length} opérandes -> ${result.length} cellules`);
    return result;
}

/**
 * Extrait les blocs de coordonnées depuis une chaîne groupée
 * Gère le format : ([0,0 2,2] [0,4 2,6]) ou [0,0 2,2] [0,4 2,6]
 */
function extractCoordinateBlocksFromGrouped(coordinates: string): string[][] {
    const blocks: string[][] = [];

    // Nettoyer les parenthèses externes si présentes
    const cleanCoords = coordinates.trim().replace(/^\(|\)$/g, '');

    // Regex pour matcher les blocs [x,y z,w] ou [x,y]
    const blockPattern = /\[([^\]]+)\]/g;
    let match;

    while ((match = blockPattern.exec(cleanCoords)) !== null) {
        const blockContent = match[1].trim();
        // Séparer les coordonnées dans le bloc par des espaces
        const coordsInBlock = blockContent.split(/\s+/).filter(coord => coord.length > 0);
        blocks.push(coordsInBlock);
    }

    return blocks;
}

/**
 * Extrait les coordonnées individuelles d'un opérande
 * Gère les formats : [1,1], [1,1 3,3], ([1,1] [2,2])
 */
function extractCoordsFromOperand(operand: string): string[] {
    const coords: string[] = [];

    // Nettoyer l'opérande
    const cleanOperand = operand.trim().replace(/^\(|\)$/g, '');

    // Extraire tous les blocs de coordonnées
    const blockRegex = /\[([^\]]+)\]/g;
    let match;

    while ((match = blockRegex.exec(cleanOperand)) !== null) {
        const blockContent = match[1].trim();

        if (blockContent.includes(' ')) {
            // Rectangle : "1,1 3,3" -> toutes les cellules du rectangle
            const parts = blockContent.split(/\s+/);
            if (parts.length === 2) {
                const [row1, col1] = parts[0].split(',').map(Number);
                const [row2, col2] = parts[1].split(',').map(Number);

                if (!isNaN(row1) && !isNaN(col1) && !isNaN(row2) && !isNaN(col2)) {
                    const minRow = Math.min(row1, row2);
                    const maxRow = Math.max(row1, row2);
                    const minCol = Math.min(col1, col2);
                    const maxCol = Math.max(col1, col2);

                    for (let r = minRow; r <= maxRow; r++) {
                        for (let c = minCol; c <= maxCol; c++) {
                            coords.push(`${r},${c}`);
                        }
                    }
                }
            }
        } else {
            // Cellule simple : "1,1"
            coords.push(blockContent);
        }
    }

    return coords;
}