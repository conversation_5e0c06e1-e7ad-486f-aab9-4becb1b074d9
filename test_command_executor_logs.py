#!/usr/bin/env python3
"""
Test du CommandExecutor avec logs pour vérifier que nos modifications sont actives
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.command_executor import CommandExecutor
from backend.command_system.unified_command import UnifiedCommand

def test_command_executor_with_logs():
    """Test du CommandExecutor avec logs"""
    print("=== Test du CommandExecutor avec logs ===")
    
    executor = CommandExecutor()
    
    commands = [
        "TRANSFERT {INIT 7x3; EDIT 1 [0,0]; EDIT 1 [0,2]; EDIT 1 [2,0]; EDIT 1 [2,2]; EDIT 5 [2,3]; EDIT 1 [2,5]}",
        "AND ([0,0 2,2] [0,4 2,6])",
        "REPLACE 1 2 [0,0 2,2]",
        "EXTRACT [0,0 2,2]",
        "END"
    ]
    
    print("Exécution des commandes avec logs de debug:")
    
    result = executor.execute_commands(commands)
    
    print(f"\nRésultat final:")
    print(f"Success: {result.get('success', False)}")
    print(f"Message: {result.get('message', 'Aucun message')}")
    
    if result.get('grid') is not None:
        print(f"Grille finale: {result['grid']}")
        
        # Convertir en liste si c'est un array numpy
        if hasattr(result['grid'], 'tolist'):
            grid_list = result['grid'].tolist()
        else:
            grid_list = result['grid']
        
        print(f"Grille finale (liste): {grid_list}")
        
        # Vérifier si le résultat correspond à l'attendu
        expected = [[2, 0, 2], [0, 0, 0], [0, 0, 0]]
        if grid_list == expected:
            print("✅ Résultat correct!")
        else:
            print("❌ Résultat incorrect!")
            print(f"Attendu: {expected}")
            print(f"Obtenu: {grid_list}")
    else:
        print("❌ Aucune grille générée!")

if __name__ == "__main__":
    test_command_executor_with_logs()