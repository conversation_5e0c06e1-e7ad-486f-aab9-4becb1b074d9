#!/usr/bin/env python3
"""
Test des opérateurs binaires AND, OR, XOR
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.command_executor import CommandExecutor
from backend.command_system.unified_command import UnifiedCommand

def test_and_operator():
    """Test de l'opérateur AND"""
    print("=== Test de l'opérateur AND ===")
    
    executor = CommandExecutor()
    
    # Initialiser une grille 7x3 (3 lignes, 7 colonnes)
    init_cmd = UnifiedCommand.parse("INIT 7x3")
    if not executor._execute_unified_command(init_cmd):
        print("❌ Échec de l'initialisation")
        return False
    
    # Créer un motif initial avec TRANSFERT
    transfert_cmd = UnifiedCommand.parse("TRANSFERT {INIT 7x3; EDIT 1 [0,0]; EDIT 1 [0,2]; EDIT 1 [2,0]; EDIT 1 [2,2]; EDIT 5 [2,3]; EDIT 1 [2,5]}")
    if not executor._execute_unified_command(transfert_cmd):
        print("❌ Échec du TRANSFERT")
        return False
    
    print("Grille après TRANSFERT:")
    print(executor.grid)
    
    # Tester l'opérateur AND
    and_cmd = UnifiedCommand.parse("AND ([0,0 2,2] [0,4 2,6])")
    if not executor._execute_unified_command(and_cmd):
        print("❌ Échec de l'opérateur AND")
        print(f"Erreur: {executor.error}")
        return False
    
    print("Grille après AND:")
    print(executor.grid)
    
    # Continuer avec REPLACE
    replace_cmd = UnifiedCommand.parse("REPLACE 1 2 [0,0 2,2]")
    if not executor._execute_unified_command(replace_cmd):
        print("❌ Échec de REPLACE")
        print(f"Erreur: {executor.error}")
        return False
    
    print("Grille après REPLACE:")
    print(executor.grid)
    
    # Finir avec EXTRACT
    extract_cmd = UnifiedCommand.parse("EXTRACT [0,0 2,2]")
    if not executor._execute_unified_command(extract_cmd):
        print("❌ Échec de EXTRACT")
        print(f"Erreur: {executor.error}")
        return False
    
    print("Grille après EXTRACT:")
    print(executor.grid)
    
    # Vérifier le résultat attendu (3x3 après EXTRACT)
    expected = [
        [2, 0, 2],
        [0, 0, 0],
        [0, 0, 0]
    ]
    
    if executor.grid.tolist() == expected:
        print("✅ Test AND complet réussi!")
        return True
    else:
        print("❌ Test AND complet échoué!")
        print(f"Attendu: {expected}")
        print(f"Obtenu: {executor.grid.tolist()}")
        return False

if __name__ == "__main__":
    test_and_operator()