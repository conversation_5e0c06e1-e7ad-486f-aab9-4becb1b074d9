// src/components/resolution/hooks/useSelection.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { enableMapSet } from 'immer';
import { type SelectionState, type SelectionRectangle, type SelectionStyle } from '../types/selectionTypes'; // CellCoord retiré
import { type SelectionSnapshot, SelectionSerializer } from '../types/syncTypes';
import { useAutomationStore } from './useAutomationStore';
import { coordToString, stringToCoord } from '../utils/selectionFormatUtils';
import { transformRelativeCoord, type SelectionTransformationType } from '../utils/selectionTransformUtils';
import { calculateSelectedCellsFromRectangles, calculateInvertedSelection, generateUUID, normalizeRectangle } from '../utils/selectionUtils';
// Removed unused import

// Réexporter le type pour qu'il soit utilisable par d'autres modules
export type { SelectionState };
 
// Import pour la grille, nécessaire pour invertSelection
// import { useResolutionStore } from './useResolutionStore'; // Remplacé par useGridStateStore
import { useGridStateStore } from './useGridStateStore'; // Import du store correct
//import { color } from '@codemirror/theme-one-dark';
// Activer le plugin MapSet pour permettre à Immer de gérer les objets Set
enableMapSet();
 
// Les fonctions coordToString et stringToCoord ont été déplacées vers selectionFormatUtils.ts

/**
 * Store Zustand pour la sélection
 */
export const useSelection = create<SelectionState>()(
  immer((set, get) => ({
    // État initial
    selectedCells: new Set<string>(),
    currentSelection: null,
    selectionMode: 'replace',
    isRecordingCommands: true, // Flag pour indiquer si nous enregistrons des commandes

    // NOUVEAUX ÉTATS pour les fonctionnalités avancées
    anchorCell: null,
    lastClickedCell: null,

    // NOUVELLE ARCHITECTURE - Collection de rectangles discontinus
    rectangleCollection: {
      rectangles: new Map<string, SelectionRectangle>(),
      activeRectangleId: null,
    },

    // REFONTE SYSTÈME SÉLECTION - État de transfert vers motif
    isSelectionActive: true, // Nouvelle propriété pour contrôler l'activité de la sélection
    hasTransferredToMotif: false, // Indique si la sélection a été transférée au motif

    // Sélectionner une cellule
    selectCell: (row, col, mode = 'replace') => {
      //console.log(`[DEBUG useSelection] selectCell appelée avec: row=${row}, col=${col}, mode=${mode}`);
      // Récupérer l'état actuel avant la modification
      const currentState = get();
      const cellKey = coordToString(row, col);
      //console.log(`[DEBUG useSelection] selectCell - Taille collection avant: ${currentState.rectangleCollection.rectangles.size}`);
      //console.log(`[DEBUG useSelection] selectCell - Cellules sélectionnées avant: ${currentState.selectedCells.size}`);
      //console.log(`[DEBUG useSelection] selectCell - Mode actuel: ${currentState.selectionMode}`);



      // Créer une nouvelle sélection basée sur le mode
      let newSelectedCells: Set<string>;

      if (mode === 'replace') {

        newSelectedCells = new Set([cellKey]);
      } else if (mode === 'add') {

        newSelectedCells = new Set(currentState.selectedCells);
        newSelectedCells.add(cellKey);
      } else if (mode === 'subtract') {

        newSelectedCells = new Set(currentState.selectedCells);
        newSelectedCells.delete(cellKey);
      } else {
        newSelectedCells = new Set(currentState.selectedCells);
      }

      // Mettre à jour l'état
      set((state) => {
        state.selectedCells = newSelectedCells;
        state.selectionMode = mode;
      });

      // Ajouter une commande d'automatisation seulement si l'enregistrement est activé
        if (get().isRecordingCommands) {
          //console.log(`[DEBUG useSelection] selectCell appelée pour [${row},${col}]`);
          try {
            const automationStore = useAutomationStore.getState();
            // Vérifier si l'enregistrement est actif dans le store d'automatisation
            if (automationStore.isRecording) {
              // Format pour cellule individuelle : SELECT [row,col]
              const command = `SELECT [${row},${col}]`;
              //const finalParams = command.replace('SELECT ', '');
              //console.log(`[DEBUG useSelection] selectCell - Paramètres envoyés: "${command}"`);
              automationStore.addCommand('SELECT', command);
              //console.log('[DEBUG useSelection] selectCell - Commande ajoutée');
            } else {
              console.log('[DEBUG useSelection] selectCell - Enregistrement non actif');
            }
          } catch (error) {
            console.error('[Selection] Failed to add automation command:', error);
          }
        }
        
        // Logs de l'état final
        //const finalState = get();
        //console.log(`[DEBUG useSelection] selectCell - Cellules sélectionnées après: ${finalState.selectedCells.size}`);
        //console.log(`[DEBUG useSelection] selectCell - Mode final: ${finalState.selectionMode}`);
        //console.log(`[DEBUG useSelection] selectCell - Taille collection après: ${finalState.rectangleCollection.rectangles.size}`);
    },

    // Sélectionner un rectangle
    selectRect: (startRow, startCol, endRow, endCol, mode = 'replace') => {
      console.log(`[DEBUG useSelection] selectRect appelée avec: startRow=${startRow}, startCol=${startCol}, endRow=${endRow}, endCol=${endCol}, mode=${mode}`);
      console.log(`[DEBUG useSelection] selectRect - Taille collection avant: ${get().rectangleCollection.rectangles.size}`);
      set((state) => {
        // Normaliser les coordonnées (pour gérer le drag dans toutes les directions)
        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minCol = Math.min(startCol, endCol);
        const maxCol = Math.max(startCol, endCol);
        
        console.log(`[DEBUG useSelection] selectRect - Coordonnées normalisées: {minRow: ${minRow}, minCol: ${minCol}, maxRow: ${maxRow}, maxCol: ${maxCol}}`);

        // Créer un ensemble temporaire pour les nouvelles cellules
        const newCells = new Set<string>();

        // Ajouter toutes les cellules du rectangle
        for (let row = minRow; row <= maxRow; row++) {
          for (let col = minCol; col <= maxCol; col++) {
            newCells.add(coordToString(row, col));
          }
        }

        // Mettre à jour la sélection selon le mode
        if (mode === 'replace') {
          state.selectedCells = newCells;
        } else if (mode === 'add') {
          newCells.forEach(cell => state.selectedCells.add(cell));
        } else if (mode === 'subtract') {
          newCells.forEach(cell => state.selectedCells.delete(cell));
        }

        // Mettre à jour la sélection rectangulaire actuelle
        state.currentSelection = {
          startRow: minRow,
          startCol: minCol,
          endRow: maxRow,
          endCol: maxCol
        };

        state.selectionMode = mode;


        // Ajouter une commande d'automatisation seulement si l'enregistrement est activé
        if (state.isRecordingCommands) {
          console.log(`[DEBUG useSelection] selectRect appelée pour [${startRow},${startCol} ${endRow},${endCol}]`);
          try {
            const automationStore = useAutomationStore.getState();
            // Vérifier si l'enregistrement est actif dans le store d'automatisation
            if (automationStore.isRecording) {
              // Vérifier si c'est une cellule individuelle ou un rectangle
              let command: string;
              if (minRow === maxRow && minCol === maxCol) {
                // Cellule individuelle : SELECT [row,col]
                command = `SELECT [${minRow},${minCol}]`;
              } else {
                // Zone rectangulaire : SELECT [startRow,startCol endRow,endCol]
                command = `SELECT [${minRow},${minCol} ${maxRow},${maxCol}]`;
              }
              const finalParams = command.replace('SELECT ', '');
              console.log(`[DEBUG useSelection] selectRect - Paramètres envoyés: "${finalParams}"`);
              automationStore.addCommand('SELECT', finalParams);
              console.log('[DEBUG useSelection] selectRect - Commande ajoutée');
            } else {
              console.log('[DEBUG useSelection] selectRect - Enregistrement non actif');
            }
          } catch (error) {
            console.error('[Selection] Failed to add automation command:', error);
          }
        }
        
        console.log(`[DEBUG useSelection] selectRect - Cellules sélectionnées: ${state.selectedCells.size}`);
        console.log(`[DEBUG useSelection] selectRect - Mode final: ${state.selectionMode}`);
      });
    },

    // Relâcher la sélection
    clearSelection: () => {
      console.log('[DEBUG useSelection] clearSelection appelée');
      //console.log('[DEBUG useSelection] clearSelection - Stack trace:', new Error().stack);
      set((state) => {
        const previousSize = state.rectangleCollection.rectangles.size;
        console.log(`[DEBUG useSelection] clearSelection - Taille collection avant: ${previousSize}`);
        
        state.selectedCells = new Set();
        state.currentSelection = null;
        state.anchorCell = null; // NOUVEAU : Effacer l'ancrage
        state.lastClickedCell = null; // NOUVEAU : Effacer la dernière cellule
        
        // PHASE 2 : Vider aussi la collection de rectangles
        state.rectangleCollection.rectangles.clear();
        state.rectangleCollection.activeRectangleId = null;
        
        console.log('[DEBUG useSelection] clearSelection - Collection vidée, nouvelle taille: 0');
      });
    },

    // Vérifier si une cellule est sélectionnée
    isSelected: (row, col) => {
      return get().selectedCells.has(coordToString(row, col));
    },

    // Obtenir un tableau des cellules sélectionnées
    getSelectedCellsArray: () => {
      return Array.from(get().selectedCells).map(stringToCoord);
    },

    // Obtenir les limites de la sélection
    getSelectionBounds: () => {
      const cells = get().getSelectedCellsArray();

      if (cells.length === 0) {
        return null;
      }

      // Trouver les limites
      let minRow = Infinity;
      let minCol = Infinity;
      let maxRow = -Infinity;
      let maxCol = -Infinity;

      cells.forEach(([row, col]) => {
        minRow = Math.min(minRow, row);
        minCol = Math.min(minCol, col);
        maxRow = Math.max(maxRow, row);
        maxCol = Math.max(maxCol, col);
      });

      return {
        startRow: minRow,
        startCol: minCol,
        endRow: maxRow,
        endCol: maxCol
      };
    },

    // Activer l'enregistrement des commandes
    enableCommandRecording: () =>
      set((state) => {
        state.isRecordingCommands = true;

      }),

    // Désactiver l'enregistrement des commandes
    disableCommandRecording: () =>
      set((state) => {
        state.isRecordingCommands = false;

      }),

    // Transformer la sélection (rotation, symétrie, multiplication, division)
    transformSelection: (transformType: SelectionTransformationType, bounds: { startRow: number, startCol: number, endRow: number, endCol: number }, factor?: number) => {
      // Récupérer l'état actuel
      const currentState = get();

      // Si pas de sélection, ne rien faire
      if (currentState.selectedCells.size === 0) return;

      // Récupérer les dimensions de la sélection
      const { startRow, startCol, endRow, endCol } = bounds;
      const height = endRow - startRow + 1;
      const width = endCol - startCol + 1;

      // Créer un ensemble pour la nouvelle sélection
      const newSelectedCells = new Set<string>();

      // Gestion spéciale pour multiply et divide qui changent les dimensions
      if (transformType === 'multiply' && factor && factor >= 2) {
        // Pour multiply : créer une sélection qui couvre la zone multipliée
        const newHeight = height * factor;
        const newWidth = width * factor;

        // Convertir la sélection actuelle en tableau de coordonnées
        const selectedCoords = Array.from(currentState.selectedCells).map(stringToCoord);

        selectedCoords.forEach(([row, col]) => {
          if (row >= startRow && row <= endRow && col >= startCol && col <= endCol) {
            // Calculer les coordonnées relatives
            const relRow = row - startRow;
            const relCol = col - startCol;

            // Multiplier : chaque cellule devient un bloc factor x factor
            for (let fi = 0; fi < factor; fi++) {
              for (let fj = 0; fj < factor; fj++) {
                const newRow = startRow + relRow * factor + fi;
                const newCol = startCol + relCol * factor + fj;
                newSelectedCells.add(coordToString(newRow, newCol));
              }
            }
          } else {
            // Conserver les cellules hors de la zone de transformation
            newSelectedCells.add(coordToString(row, col));
          }
        });

      } else if (transformType === 'divide' && factor && factor >= 2) {
        // Pour divide : créer une sélection échantillonnée
        const newHeight = Math.floor(height / factor);
        const newWidth = Math.floor(width / factor);

        // Convertir la sélection actuelle en tableau de coordonnées
        const selectedCoords = Array.from(currentState.selectedCells).map(stringToCoord);

        selectedCoords.forEach(([row, col]) => {
          if (row >= startRow && row <= endRow && col >= startCol && col <= endCol) {
            // Calculer les coordonnées relatives
            const relRow = row - startRow;
            const relCol = col - startCol;

            // Diviser : échantillonner une cellule sur factor
            if (relRow % factor === 0 && relCol % factor === 0) {
              const newRow = startRow + Math.floor(relRow / factor);
              const newCol = startCol + Math.floor(relCol / factor);
              newSelectedCells.add(coordToString(newRow, newCol));
            }
          } else {
            // Conserver les cellules hors de la zone de transformation
            newSelectedCells.add(coordToString(row, col));
          }
        });

      } else {
        // Transformations classiques (rotation, flip)
        const selectedCoords = Array.from(currentState.selectedCells).map(stringToCoord);

        selectedCoords.forEach(([row, col]) => {
          // Vérifier si la cellule est dans les limites de la sélection
          if (row >= startRow && row <= endRow && col >= startCol && col <= endCol) {
            // Calculer les coordonnées relatives à l'origine de la sélection
            const relRow = row - startRow;
            const relCol = col - startCol;

            // Appliquer la transformation en utilisant la fonction utilitaire
            const { row: newRelRow, col: newRelCol } = transformRelativeCoord(
              { row: relRow, col: relCol },
              transformType,
              height,
              width,
              factor
            );

            // Calculer les nouvelles coordonnées absolues
            const newRow = startRow + newRelRow;
            const newCol = startCol + newRelCol;

            // Ajouter la cellule transformée à la nouvelle sélection
            newSelectedCells.add(coordToString(newRow, newCol));
          } else {
            // Si la cellule n'est pas dans les limites de la sélection, la conserver telle quelle
            newSelectedCells.add(coordToString(row, col));
          }
        });
      }

      // Mettre à jour la sélection en utilisant set
      set((state) => {
        // Remplacer l'ensemble de cellules sélectionnées
        state.selectedCells.clear();
        newSelectedCells.forEach(cell => state.selectedCells.add(cell));

        // Mettre à jour la sélection rectangulaire actuelle si nécessaire
        if (state.currentSelection) {
          if (transformType === 'rotateRight' || transformType === 'rotateLeft') {
            // Pour les rotations, les dimensions sont inversées
            state.currentSelection = {
              startRow,
              startCol,
              endRow: startRow + width - 1,
              endCol: startCol + height - 1
            };
          }
          // Pour les symétries, les dimensions restent les mêmes
        }
      });


    },

    // Déplacer la sélection
    moveSelection: (_direction: 'up' | 'down' | 'left' | 'right', oldPosition: { row: number, col: number }, newPosition: { row: number, col: number }) => {

      // Récupérer l'état actuel
      const currentState = get();

      // Si pas de sélection, ne rien faire
      if (currentState.selectedCells.size === 0) {
        return;
      }

      // Calculer le décalage
      const rowOffset = newPosition.row - oldPosition.row;
      const colOffset = newPosition.col - oldPosition.col;

      // Créer un ensemble pour la nouvelle sélection
      const newSelectedCells = new Set<string>();

      // Convertir la sélection actuelle en tableau de coordonnées
      const selectedCoords = Array.from(currentState.selectedCells).map(stringToCoord);

      // Appliquer le décalage à chaque cellule sélectionnée
      selectedCoords.forEach(([row, col]) => {
        const newRow = row + rowOffset;
        const newCol = col + colOffset;
        const newCell = coordToString(newRow, newCol);

        // Ajouter la cellule déplacée à la nouvelle sélection
        newSelectedCells.add(newCell);
      });



      // Mettre à jour la sélection en utilisant set
      set((state) => {
        // CORRECTION RÉACTIVITÉ: Créer un nouveau Set au lieu de modifier l'existant
        // pour déclencher le re-render React
        state.selectedCells = new Set(newSelectedCells);

        // CORRECTION: Mettre à jour aussi les rectangles de la rectangleCollection
        if (state.rectangleCollection && state.rectangleCollection.rectangles.size > 0) {

          
          // Créer une nouvelle Map pour les rectangles déplacés
          const newRectangles = new Map();
          
          state.rectangleCollection.rectangles.forEach((rectangle, id) => {
            const newRectangle = {
              ...rectangle,
              startRow: rectangle.startRow + rowOffset,
              startCol: rectangle.startCol + colOffset,
              endRow: rectangle.endRow + rowOffset,
              endCol: rectangle.endCol + colOffset
            };
            newRectangles.set(id, newRectangle);
          });
          
          // Remplacer la Map des rectangles
          state.rectangleCollection.rectangles.clear();
          newRectangles.forEach((rectangle, id) => {
            state.rectangleCollection.rectangles.set(id, rectangle);
          });
          

        }

        // CORRECTION: Mettre à jour l'anchorCell aussi lors du déplacement
        if (state.anchorCell) {
          const oldAnchor = state.anchorCell;
          state.anchorCell = [oldAnchor[0] + rowOffset, oldAnchor[1] + colOffset];

        }

        // Mettre à jour la sélection rectangulaire actuelle si nécessaire
        if (state.currentSelection) {
          state.currentSelection = {
            startRow: state.currentSelection.startRow + rowOffset,
            startCol: state.currentSelection.startCol + colOffset,
            endRow: state.currentSelection.endRow + rowOffset,
            endCol: state.currentSelection.endCol + colOffset
          };
        }
      });
    },

    // NOUVELLES MÉTHODES pour les fonctionnalités avancées

    // Définir la cellule d'ancrage
    setAnchorCell: (cell) =>
      set((state) => {
        state.anchorCell = cell;

      }),

    // Récupérer la cellule d'ancrage
    getAnchorCell: () => {
      return get().anchorCell;
    },

    // Sélectionner une zone rectangulaire entre deux cellules
    selectRange: (start, end, mode = 'replace') =>
      set((state) => {
        // Calculer les limites de la zone rectangulaire
        const [startRow, startCol] = start;
        const [endRow, endCol] = end;
        
        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minCol = Math.min(startCol, endCol);
        const maxCol = Math.max(startCol, endCol);

        // Créer un ensemble pour les nouvelles cellules
        const newCells = new Set<string>();
        
        // Ajouter toutes les cellules de la zone
        for (let row = minRow; row <= maxRow; row++) {
          for (let col = minCol; col <= maxCol; col++) {
            newCells.add(`${row},${col}`);
          }
        }

        // Appliquer selon le mode
        if (mode === 'replace') {
          state.selectedCells = newCells;
        } else if (mode === 'add') {
          newCells.forEach(cell => state.selectedCells.add(cell));
        } else if (mode === 'subtract') {
          newCells.forEach(cell => state.selectedCells.delete(cell));
        }

        // Mettre à jour la sélection rectangulaire actuelle
        state.currentSelection = {
          startRow: minRow,
          startCol: minCol,
          endRow: maxRow,
          endCol: maxCol
        };

        state.selectionMode = mode;

        // Enregistrement automatique des commandes
        if (state.isRecordingCommands) {
          try {
            const automationStore = useAutomationStore.getState();
            if (automationStore.isRecording) {
              // Vérifier si c'est une cellule individuelle ou un rectangle
              let command: string;
              if (minRow === maxRow && minCol === maxCol) {
                // Cellule individuelle : SELECT [row,col]
                command = `SELECT [${minRow},${minCol}]`;
              } else {
                // Zone rectangulaire : SELECT [startRow,startCol endRow,endCol]
                command = `SELECT [${minRow},${minCol} ${maxRow},${maxCol}]`;
              }
              automationStore.addCommand('SELECT', command.replace('SELECT ', ''));
            }
          } catch (error) {
            console.error('[Selection] Failed to add range selection command:', error);
          }
        }
      }),

    // Vérifier si une cellule est l'ancrage
    isAnchorCell: (row, col) => {
      const anchor = get().anchorCell;
      return anchor ? anchor[0] === row && anchor[1] === col : false;
    },

    // PHASE 2 - NOUVELLES MÉTHODES DE GESTION DES RECTANGLES MULTIPLES

    // Ajouter un nouveau rectangle à la collection
    addRectangle: (startRow, startCol, endRow, endCol, style) =>
      set((state: SelectionState) => {
        //console.log(`[DEBUG useSelection] addRectangle appelée avec: startRow=${startRow}, startCol=${startCol}, endRow=${endRow}, endCol=${endCol}, style=${style}`);
        //console.log('[DEBUG useSelection] Taille actuelle de la collection:', state.rectangleCollection.rectangles.size);
        
        // Normaliser les coordonnées
        const normalized = normalizeRectangle(startRow, startCol, endRow, endCol);
        //console.log('[DEBUG useSelection] Coordonnées normalisées:', normalized);
        
        // Déterminer le style
        let rectStyle: SelectionStyle = style || 'primary';
        if (!style) {
          // Rotation automatique des styles si pas spécifié
          const count = state.rectangleCollection.rectangles.size;
          const styles: SelectionStyle[] = ['primary', 'secondary', 'accent'];
          rectStyle = styles[count % styles.length];
        }
        //console.log('[DEBUG useSelection] Style déterminé:', rectStyle);

        // Créer le nouveau rectangle
        const newRectangle: SelectionRectangle = {
          id: generateUUID(),
          ...normalized,
          createdAt: Date.now(),
          style: rectStyle,
        };
        //console.log('[DEBUG useSelection] Nouveau rectangle créé:', newRectangle);

        // Ajouter à la collection
        state.rectangleCollection.rectangles.set(newRectangle.id, newRectangle);
        //console.log('[DEBUG useSelection] Rectangle ajouté, nouvelle taille:', state.rectangleCollection.rectangles.size);
        
        // Si c'est le premier rectangle, le définir comme actif
        if (state.rectangleCollection.rectangles.size === 1) {
          state.rectangleCollection.activeRectangleId = newRectangle.id;
          //console.log('[DEBUG useSelection] Premier rectangle défini comme actif:', newRectangle.id);
        }

        // Recalculer le cache des cellules sélectionnées
        state.selectedCells = calculateSelectedCellsFromRectangles(state.rectangleCollection.rectangles);
        //console.log('[DEBUG useSelection] Cellules sélectionnées recalculées, nombre:', state.selectedCells.size);
        
        // La commande SELECT sera générée lors de la finalisation de la sélection
      }),

    // Supprimer un rectangle de la collection
    removeRectangle: (rectangleId) =>
      set((state: SelectionState) => {
        //console.log(`[DEBUG useSelection] removeRectangle appelée avec rectangleId: ${rectangleId}`);
        //console.log('[DEBUG useSelection] Taille actuelle de la collection:', state.rectangleCollection.rectangles.size);
        //console.log('[DEBUG useSelection] Rectangle existe?', state.rectangleCollection.rectangles.has(rectangleId));
        
        if (state.rectangleCollection.rectangles.has(rectangleId)) {
          console.log('[DEBUG useSelection] Suppression du rectangle:', rectangleId);
          state.rectangleCollection.rectangles.delete(rectangleId);
          console.log('[DEBUG useSelection] Nouvelle taille après suppression:', state.rectangleCollection.rectangles.size);
          
          // Si c'était le rectangle actif, choisir un autre ou mettre à null
          if (state.rectangleCollection.activeRectangleId === rectangleId) {
            console.log('[DEBUG useSelection] Rectangle actif supprimé, recherche d\'un remplaçant');
            const remaining = Array.from(state.rectangleCollection.rectangles.keys());
            state.rectangleCollection.activeRectangleId = remaining.length > 0 ? remaining[0] : null;
            console.log('[DEBUG useSelection] Nouveau rectangle actif:', state.rectangleCollection.activeRectangleId);
          }

          // Recalculer le cache des cellules sélectionnées
          state.selectedCells = calculateSelectedCellsFromRectangles(state.rectangleCollection.rectangles);
          console.log('[DEBUG useSelection] Cellules sélectionnées recalculées, nombre:', state.selectedCells.size);
        } else {
          console.log('[DEBUG useSelection] Rectangle non trouvé, aucune suppression effectuée');
        }
        
        // La commande SELECT sera générée lors de la finalisation de la sélection
      }),

    // Vider tous les rectangles
    clearRectangles: () => {
      //console.log('[DEBUG useSelection] clearRectangles appelée');
      //console.log('[DEBUG useSelection] clearRectangles - Stack trace:', new Error().stack);
      set((state: SelectionState) => {
        const previousSize = state.rectangleCollection.rectangles.size;
        //console.log(`[DEBUG useSelection] clearRectangles - Taille collection avant: ${previousSize}`);
        
        state.rectangleCollection.rectangles.clear();
        state.rectangleCollection.activeRectangleId = null;
        
        // Recalculer le cache des cellules sélectionnées (sera vide)
        state.selectedCells.clear();
        
        //console.log('[DEBUG useSelection] clearRectangles - Collection vidée, nouvelle taille: 0');
        
        // La commande SELECT sera générée lors de la finalisation de la sélection
      });
    },

    // Définir le rectangle actif
    setActiveRectangle: (rectangleId) =>
      set((state) => {
        if (rectangleId === null || state.rectangleCollection.rectangles.has(rectangleId)) {
          state.rectangleCollection.activeRectangleId = rectangleId;

        }
      }),

    // Recalculer le cache des cellules sélectionnées depuis la collection de rectangles
    recalculateSelectedCells: () =>
      set((state) => {
        state.selectedCells = calculateSelectedCellsFromRectangles(state.rectangleCollection.rectangles);

      }),

    // Obtenir tous les rectangles contenant une cellule donnée
    getCellRectangles: (row, col) => {
      const rectangles: SelectionRectangle[] = [];
      const collection = get().rectangleCollection;
      
      collection.rectangles.forEach((rectangle) => {
        if (row >= rectangle.startRow && row <= rectangle.endRow &&
            col >= rectangle.startCol && col <= rectangle.endCol) {
          rectangles.push(rectangle);
        }
      });

      if (rectangles.length > 0) {

      }

      return rectangles;
    },

    // Obtenir le nombre de rectangles dans la collection
    getRectangleCount: () => {
      return get().rectangleCollection.rectangles.size;
    },

    // Vérifier si la collection contient des rectangles
    hasRectangles: () => {
      return get().rectangleCollection.rectangles.size > 0;
    },

    // Capturer un snapshot de l'état actuel des sélections
    captureSnapshot: (): SelectionSnapshot => {
      const state = get();
      
      try {
        // Récupérer les commandes SELECT récentes
        const recentCommands = state.getRecentSelectCommands();
        
        // Créer le snapshot avec l'état actuel
        const snapshot: SelectionSnapshot = {
          rectangleCollection: {
            rectangles: new Map(state.rectangleCollection.rectangles),
            activeRectangleId: state.rectangleCollection.activeRectangleId
          },
          anchorCell: state.anchorCell,
          selectedCells: new Set(state.selectedCells),
          timestamp: Date.now(),
          lastCommandIndex: (() => {
            try {
              const automationStore = useAutomationStore.getState();
              const commands = automationStore.commands[automationStore.currentTestIndex] || [];
              return commands.length;
            } catch {
              return 0;
            }
          })(),
          associatedCommands: recentCommands,
          isRecordingCommands: state.isRecordingCommands,
          selectionMode: state.selectionMode,
          version: '2.0'
        };

        return snapshot;
      } catch (error) {
        console.error('[Selection] Failed to capture snapshot:', error);
        // Retourner un snapshot minimal en cas d'erreur
        return {
          rectangleCollection: {
            rectangles: new Map(),
            activeRectangleId: null
          },
          anchorCell: null,
          selectedCells: new Set(),
          timestamp: Date.now(),
          lastCommandIndex: 0,
          associatedCommands: [],
          isRecordingCommands: state.isRecordingCommands,
          selectionMode: state.selectionMode,
          version: '2.0'
        };
      }
    },

    // Restaurer l'état depuis un snapshot
    restoreSnapshot: (snapshot: SelectionSnapshot) => {
      try {
        // Valider le snapshot avant restauration
        const serialized = SelectionSerializer.serialize(snapshot);
        const validation = SelectionSerializer.validate(serialized);
        
        if (!validation.isValid) {
          console.error('[Selection] Invalid snapshot, aborting restore:', validation.errors);
          return;
        }

        set((state) => {
          // Restaurer la collection de rectangles
          state.rectangleCollection.rectangles.clear();
          snapshot.rectangleCollection.rectangles.forEach((rectangle, id) => {
            state.rectangleCollection.rectangles.set(id, rectangle);
          });
          state.rectangleCollection.activeRectangleId = snapshot.rectangleCollection.activeRectangleId;
          
          // Restaurer le contexte de sélection
          state.anchorCell = snapshot.anchorCell ? [...snapshot.anchorCell] : null; // Cloner l'anchorCell
          state.selectedCells.clear();
          snapshot.selectedCells.forEach(cell => state.selectedCells.add(cell));
          state.isRecordingCommands = snapshot.isRecordingCommands;
          state.selectionMode = snapshot.selectionMode;


        });
      } catch (error) {
        console.error('[Selection] Failed to restore snapshot:', error);
        // En cas d'erreur, vider les sélections pour éviter un état incohérent
        get().clearSelection();
      }
    },
    
    // Récupérer les commandes SELECT récentes depuis le dernier snapshot
    getRecentSelectCommands: (): string[] => {
      try {
        const automationStore = useAutomationStore.getState();
        const commands = automationStore.commands[automationStore.currentTestIndex] || [];
        
        // Filtrer les commandes SELECT récentes (par ex. les 10 dernières)
        const recentCommands = commands
          .slice(-10) // Prendre les 10 dernières commandes
          .filter(cmd => cmd.startsWith('SELECT'));
        
        return recentCommands;
      } catch (error) {
        console.error('[Selection] Failed to get recent SELECT commands:', error);
        return [];
      }
    },

    // Marquer l'index de commande actuel (pour synchronisation)
    markCommandIndex: (index: number) => {
      // Cette méthode sera utilisée par le système d'automatisation
      // pour marquer le point de synchronisation avec les sélections
      console.log('[Selection] Command index marked:', index);
      // Stockage temporaire de l'index si nécessaire
      // (peut être étendu selon les besoins)
    },

    // NOUVELLES MÉTHODES POUR SYNCHRONISATION DES TRANSFORMATIONS

    // Méthode pour mise à jour synchronisée AVANT transformation
    updateSelectionBeforeTransform: (
      newCoordinates: Set<string>,
      newBounds: { startRow: number, startCol: number, endRow: number, endCol: number },
      transformType: 'rotateRight' | 'rotateLeft' | 'flipHorizontal' | 'flipVertical'
    ) => {
      set((state) => {
        // Vider la sélection actuelle
        state.selectedCells.clear();
        
        // Ajouter les nouvelles coordonnées
        newCoordinates.forEach(coord => state.selectedCells.add(coord));
        
        // Mettre à jour les bounds selon le type de transformation
        if (transformType === 'rotateRight' || transformType === 'rotateLeft') {
          // Pour les rotations, inverser largeur et hauteur
          const originalHeight = newBounds.endRow - newBounds.startRow + 1;
          const originalWidth = newBounds.endCol - newBounds.startCol + 1;
          state.currentSelection = {
            startRow: newBounds.startRow,
            startCol: newBounds.startCol,
            endRow: newBounds.startRow + originalWidth - 1,
            endCol: newBounds.startCol + originalHeight - 1
          };
        } else {
          // Pour les symétries, garder les mêmes dimensions
          state.currentSelection = { ...newBounds };
        }
        
        console.log('[Selection] Updated before transform:', {
          newCells: newCoordinates.size,
          newBounds: state.currentSelection,
          transformType
        });
      });
    },

    // Méthode pour validation post-transformation
    validateTransformationConsistency: (
      expectedCoordinates: Set<string>,
      expectedBounds: { startRow: number, startCol: number, endRow: number, endCol: number }
    ): boolean => {
      const state = get();
      
      // Vérifier que les coordonnées correspondent
      if (state.selectedCells.size !== expectedCoordinates.size) {
        console.error('[Selection] Consistency check failed: cell count mismatch', {
          expected: expectedCoordinates.size,
          actual: state.selectedCells.size
        });
        return false;
      }
      
      for (const coord of expectedCoordinates) {
        if (!state.selectedCells.has(coord)) {
          console.error('[Selection] Consistency check failed: missing coordinate', coord);
          return false;
        }
      }
      
      // Vérifier les bounds
      const currentBounds = state.currentSelection;
      if (!currentBounds) {
        console.error('[Selection] Consistency check failed: no current selection bounds');
        return false;
      }
      
      const boundsMatch = (
        currentBounds.startRow === expectedBounds.startRow &&
        currentBounds.startCol === expectedBounds.startCol &&
        currentBounds.endRow === expectedBounds.endRow &&
        currentBounds.endCol === expectedBounds.endCol
      );

      if (!boundsMatch) {
        console.error('[Selection] Consistency check failed: bounds mismatch', {
          expected: expectedBounds,
          actual: currentBounds
        });
        return false;
      }

      console.log('[Selection] Consistency check passed');
      return true;
    },

    // Méthode pour rollback de sélection
    rollbackSelection: (snapshot: SelectionSnapshot) => {
      try {
        get().restoreSnapshot(snapshot);
        console.log('[Selection] Rollback completed successfully');
      } catch (error) {
        console.error('[Selection] Rollback failed:', error);
        // En cas d'échec, vider la sélection pour éviter un état incohérent
        get().clearSelection();
      }
    },

    // NOUVELLES MÉTHODES POUR REFONTE SYSTÈME SÉLECTION - Transfert vers motif

    // CORRECTION ARCHITECTURE #5 - Transférer la sélection multiple vers le motif
    transferToClipboard: () => {
      const state = get();
      
      // Vérifier qu'il y a une sélection multiple à transférer
      if (state.rectangleCollection.rectangles.size === 0) {
        console.warn('[Selection] No multiple selection to transfer to clipboard');
        return false;
      }

      set((draft) => { //EGA
        // NOUVELLE ARCHITECTURE : La sélection multiple devient le système principal
        console.log('[Selection] Converting multiple selection to motif system');
        
        // Changer la couleur de la sélection multiple pour qu'elle devienne rouge (motif)
        draft.rectangleCollection.rectangles.forEach((rectangle) => {
          rectangle.style = 'accent'; // Changement vers style rouge (motif)
        });
        
        // Effacer l'ancienne sélection standard (blanche) qui ne doit plus exister
        draft.selectedCells.clear();
        draft.currentSelection = null;
        
        // Marquer la sélection comme transférée et la désactiver visuellement
        draft.isSelectionActive = false;
        draft.hasTransferredToMotif = true;
        
        console.log('[Selection] Multiple selection transferred to motif system:', {
          rectangleCount: draft.rectangleCollection.rectangles.size,
          oldSystemCleared: true,
          newMotifActive: true
        });
      });

      // Enregistrer la commande de transfert
      // if (state.isRecordingCommands) {
      //   try {
      //     const automationStore = useAutomationStore.getState();
      //     if (automationStore.isRecording) {
      //       automationStore.addUnifiedCommand('TRANSFER_SELECTION');
      //     }
      //   } catch (error) {
      //     console.error('[Selection] Failed to add TRANSFER_SELECTION command:', error);
      //   }
      // }

      return true;
    },

    // Désactiver visuellement la sélection standard
    deactivateSelection: () => {
      set((state) => {
        state.isSelectionActive = false;
        console.log('[Selection] Selection deactivated visually');
      });
    },

    // Réactiver la sélection standard (après coller ou annulation)
    reactivateSelection: () => {
      set((state) => {
        state.isSelectionActive = true;
        state.hasTransferredToMotif = false;
        console.log('[Selection] Selection reactivated');
      });
    },

    // Vérifier si la sélection est actuellement active
    isSelectionActiveState: () => {
      return get().isSelectionActive;
    },

    // Vérifier si la sélection a été transférée vers le motif
    hasBeenTransferredToMotif: () => {
      return get().hasTransferredToMotif;
    },

    // Effacer toutes les sélections (standard + motif) - utilisé après collage
    clearAllSelections: () => {
      set((state) => {
        // Vider la sélection standard
        state.selectedCells.clear();
        state.currentSelection = null;
        state.anchorCell = null;
        state.lastClickedCell = null;
        state.rectangleCollection.rectangles.clear();
        state.rectangleCollection.activeRectangleId = null;
        
        // Réactiver la sélection pour le prochain cycle
        state.isSelectionActive = true;
        state.hasTransferredToMotif = false;
      });
    },

    // Inverser la sélection actuelle
    invertSelection: () => {
      const { getCurrentGrid } = useGridStateStore.getState(); // Utiliser useGridStateStore
      const currentGrid = getCurrentGrid();
      if (!currentGrid) return;
 
      set((state) => {
        const newSelectedCells = calculateInvertedSelection(currentGrid.height, currentGrid.width, state.selectedCells);
        
        state.selectedCells = newSelectedCells;
        // Après une inversion, il n'y a plus de "rectangle actif" unique ni d'ancre pertinente
        state.currentSelection = null;
        state.anchorCell = null;
        state.rectangleCollection.rectangles.clear(); // Vider les rectangles car l'inversion ne correspond plus à des rectangles simples
        state.rectangleCollection.activeRectangleId = null;

        // Enregistrement automatique de la commande avec le nouveau format
        if (state.isRecordingCommands) {
          try {
            const automationStore = useAutomationStore.getState();
            if (automationStore.isRecording) {
              // Nouveau format : remplacer la dernière commande SELECT par SELECT (INVERT ([coords]))
              const currentCommands = automationStore.getCurrentCommands();
              if (currentCommands.length > 0) {
                const lastCommand = currentCommands[currentCommands.length - 1];
                if (lastCommand.startsWith('SELECT ')) {
                  // Extraire les coordonnées de la commande SELECT existante
                  const coordsMatch = lastCommand.match(/SELECT\s+(.+)/);
                  if (coordsMatch) {
                    const coords = coordsMatch[1];
                    const newParams = `(INVERT ${coords})`;
                    // Utiliser replaceOrAddSelectCommand pour remplacer la dernière commande SELECT
                    automationStore.replaceOrAddSelectCommand(newParams);
                    console.log(`[Selection] Transformed SELECT to INVERT: ${lastCommand} -> SELECT ${newParams}`);
                  }
                } else {
                  // Si pas de SELECT précédent, créer une nouvelle commande avec les coordonnées de toute la grille
                  const fullGridCoords = `[0,0 ${currentGrid.height-1},${currentGrid.width-1}]`;
                  automationStore.replaceOrAddSelectCommand(`(INVERT ${fullGridCoords})`);
                }
              } else {
                // Si pas de commandes précédentes, créer une nouvelle commande avec les coordonnées de toute la grille
                const fullGridCoords = `[0,0 ${currentGrid.height-1},${currentGrid.width-1}]`;
                automationStore.replaceOrAddSelectCommand(`(INVERT ${fullGridCoords})`);
              }
            }
          } catch (error) {
            console.error('[Selection] Failed to add INVERT command:', error);
          }
        }
        console.log('[Selection] Selection inverted. New selection size:', newSelectedCells.size);
      });
    },

    // Filtrer la sélection actuelle par la palette de couleurs
    colorSelection: () => {
      console.log('[useSelection] colorSelection Filtering selection by color');
      const { getCurrentGrid } = useGridStateStore.getState(); // Utiliser useGridStateStore
      const currentGrid = getCurrentGrid();
      if (!currentGrid) return;
 
      set((state) => {
        // Enregistrement automatique de la commande avec le nouveau format
        if (state.isRecordingCommands) {
          try {
            const automationStore = useAutomationStore.getState();
            if (automationStore.isRecording) {
              // Nouveau format : remplacer la dernière commande SELECT par SELECT (COLOR [colors] ([coords]))
              const currentCommands = automationStore.getCurrentCommands();
              if (currentCommands.length > 0) {
                const lastCommand = currentCommands[currentCommands.length - 1];
                if (lastCommand.startsWith('SELECT ')) {
                  // Extraire les coordonnées de la commande SELECT existante
                  // const coordsMatch = lastCommand.match(/SELECT\s+(.+)/);
                  // if (coordsMatch) {
                  //   const newParams = `(${lastCommand})`;
                    automationStore.replaceOrAddSelectCommand(`(COLOR ${lastCommand})`);
                    console.log(`[Selection] Transformed SELECT to COLOR: ${lastCommand} -> SELECT ${lastCommand}`);
                //   }
                // } else {
                //   console.error('[Selection] Failed to add COLOR command:', lastCommand);
                }
              } else {
                console.error('[Selection] Failed to add COLOR command:');
              }
            }
          } catch (error) {
            console.error('[Selection] Failed to add COLOR command:', error);
          }
        }
        console.log('[Selection] Selection filtered by color. New selection size:'); //, filteredCells.size);
      });
    },

    // Méthode pour synchroniser l'état d'enregistrement avec le store d'automatisation
    setRecordingCommands: (isRecording: boolean) => set((state) => {
      state.isRecordingCommands = isRecording;
    }),

    // Ajout d'une commande SELECT - Version simplifiée
    addSelectMultipleCommand: (rectangles: SelectionRectangle[]) => {
      const automationStore = useAutomationStore.getState();
      if (!automationStore.isRecording || !rectangles?.length) return;
      
      // Formater chaque rectangle en coordonnées
      const coords = rectangles.map(rect => 
        rect.startRow === rect.endRow && rect.startCol === rect.endCol 
          ? `[${rect.startRow},${rect.startCol}]`  // Cellule unique
          : `[${rect.startRow},${rect.startCol} ${rect.endRow},${rect.endCol}]`  // Rectangle
      );
      
      // Créer la commande avec parenthèses si multiple sélections
      const params = coords.length === 1 ? coords[0] : `(${coords.join(' ')})`;
      automationStore.replaceOrAddSelectCommand(params);
    },
  }))
);

// Fonction utilitaire pour déclencher la commande SELECT conforme
// La génération de commande SELECT est maintenant gérée uniquement dans handleMouseUp de ResolutionGrid
