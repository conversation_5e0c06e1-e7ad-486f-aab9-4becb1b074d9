﻿"""
UnifiedCommand - Parser moderne pour commandes de scénarios ARC
Version refactorisée - Support uniquement des commandes modernes
"""

import re
from typing import List, Dict, Any, Optional


class UnifiedCommand:
    """
    Parser moderne pour commandes unifiées selon FONCTIONNEMENT_COMMANDES_SCENARIOS.md

    Commandes supportées :
    - Actions de base : INIT, EDIT, FILL, CLEAR, SURROUND, REPLACE
    - Modifications structurelles : INSERT, DELETE, EXTRACT
    - Transformations : FLIP HORIZONTAL/VERTICAL, ROTATE LEFT/RIGHT
    - Presse-papier : COPY, CUT, PASTE
    - Système : TRANSFERT, END
    - Modificateurs : (INVERT ...), (COLOR ...)
    """

    def __init__(self, action: str, parameters: List[Any] = None, coordinates: List[int] = None,
                 coordinate_blocks: List[List[str]] = None, raw_command: str = ""):
        """
        Initialise une commande unifiée moderne avec support des blocs sémantiques

        Args:
            action: L'action de la commande (INIT, EDIT, FILL, etc.)
            parameters: Liste des paramètres (couleurs, dimensions, etc.)
            coordinates: Liste des coordonnées [ligne,colonne,...] (compatibilité)
            coordinate_blocks: Blocs sémantiques de coordonnées (nouveau système)
            raw_command: Commande originale sous forme de chaîne
        """
        self.action = action.upper()
        self.parameters = parameters or []
        self.coordinates = coordinates or []
        self.coordinate_blocks = coordinate_blocks or []  # Nouveau: blocs sémantiques
        self.raw_command = raw_command

    @classmethod
    def parse(cls, command: str) -> Optional['UnifiedCommand']:
        """
        Parse une commande moderne selon FONCTIONNEMENT_COMMANDES_SCENARIOS.md

        Formats supportés :
        - Actions de base : INIT 3x3, EDIT 7 [0,0], FILL 5 [1,2 3,4]
        - Modifications : INSERT 2 ROWS ABOVE [1,1], DELETE COLUMNS [0,1 2,1]
        - Transformations : FLIP HORIZONTAL [1,2 3,4], ROTATE LEFT [0,0 2,2]
        - Système : TRANSFERT {INIT 3x3; EDIT 7 [0,0]}, END
        - Modificateurs : CLEAR (INVERT [1,1 3,3]), FILL 5 (COLOR 1,2 [0,0 4,4])

        Args:
            command: La commande à parser

        Returns:
            UnifiedCommand ou None si le parsing échoue
        """
        if not command or not command.strip():
            return None

        command = command.strip()

        # Rejeter les commandes obsolètes
        if cls._is_obsolete_command(command):
            return None

        # 1. Commandes avec accolades : TRANSFERT {commandes} ou MOTIF {commandes}
        if '{' in command and '}' in command:
            return cls._parse_brace_command(command)

        # 2. Commandes avec coordonnées multiples : ACTION params ([coord1] [coord2] ...)
        if '(' in command and ')' in command and '[' in command and ']' in command:
            multi_result = cls._parse_multi_coordinates_command(command)
            if multi_result:
                return multi_result

        # 3. Commandes avec modificateurs : ACTION (INVERT/COLOR ...)
        if '(' in command and ')' in command:
            return cls._parse_modified_command(command)

        # 4. Commandes standards : ACTION paramètres [coordonnées]
        return cls._parse_standard_command(command)

    @classmethod
    def _is_obsolete_command(cls, command: str) -> bool:
        """Vérifie si une commande utilise un format obsolète"""
        obsolete_patterns = [
            r'^(ROTATE_R|ROTATE_L|FLIP_H|FLIP_V|INSERT_R|INSERT_V|DELETE_R|DELETE_L)',  # Commandes avec underscores
            r'^(PROPOSE|VALIDATE)\b',             # Commandes obsolètes
            r'^SELECT_(INVERT|COLOR)\b'           # Anciens formats de sélection
        ]

        for pattern in obsolete_patterns:
            if re.match(pattern, command.upper()):
                return True
        return False

    @classmethod
    def _parse_brace_command(cls, command: str) -> Optional['UnifiedCommand']:
        """Parse les commandes avec accolades : TRANSFERT {contenu} ou MOTIF {contenu}"""
        # Pattern pour TRANSFERT ou MOTIF
        pattern = r'^(TRANSFERT|MOTIF)\s*\{([^}]+)\}$'
        match = re.match(pattern, command, re.IGNORECASE)

        if not match:
            return None

        action = match.group(1).upper()
        content = match.group(2).strip()
        return cls(action, [content], [], [], command)

    @classmethod
    def _parse_multi_coordinates_command(cls, command: str) -> Optional['UnifiedCommand']:
        """Parse les commandes avec coordonnées multiples : ACTION params ([coord1] [coord2] ...)"""
        # Pattern pour coordonnées multiples avec parenthèses
        pattern = r'^([A-Z_\s]+?)(?:\s+([^\(]+?))?\s*\((.+)\)$'
        match = re.match(pattern, command.strip())

        if not match:
            return None

        action = match.group(1).strip().upper()
        params_str = match.group(2)
        coords_content = match.group(3)

        # Vérifier qu'il y a plusieurs blocs de coordonnées
        coord_blocks = re.findall(r'\[([^\]]+)\]', coords_content)
        if len(coord_blocks) <= 1:
            return None  # Pas un format multi-coordonnées valide

        # Parser les paramètres
        parameters = cls._parse_parameters(params_str.strip(), action) if params_str and params_str.strip() else []

        # Parser toutes les coordonnées
        coordinates = []
        for block in coord_blocks:
            coordinates.extend(cls._parse_coordinates(block))

        # Parser les blocs de coordonnées pour préserver la sémantique
        coordinate_blocks = cls._parse_coordinate_blocks(command)

        return cls(action, parameters, coordinates, coordinate_blocks, command)

    @classmethod
    def _parse_modified_command(cls, command: str) -> Optional['UnifiedCommand']:
        """Parse les commandes avec modificateurs : ACTION (INVERT/COLOR ...)"""

        # Format 1 : ACTION (COLOR params [coords]) - Format réel utilisé dans les scénarios
        pattern1 = r'^(\w+(?:\s+\w+)*)\s*\((COLOR)\s+([^[]+?)\s*(\[.+\])\)$'
        match1 = re.match(pattern1, command)

        if match1:
            action = match1.group(1).upper()
            modifier = match1.group(2).upper()
            modifier_params = match1.group(3).strip()
            coords_content = match1.group(4)

            # Parser les coordonnées du modificateur
            coordinates = cls._extract_coordinates_from_content(coords_content)

            # Pour les modificateurs, on stocke l'information dans raw_command
            coordinate_blocks = cls._parse_coordinate_blocks(command)
            return cls(action, [], coordinates, coordinate_blocks, command)

        # Format 2 : ACTION (INVERT [coords]) - Format INVERT
        pattern2 = r'^(\w+(?:\s+\w+)*)\s*\((INVERT)\s*(\[.+\])\)$'
        match2 = re.match(pattern2, command)

        if match2:
            action = match2.group(1).upper()
            modifier = match2.group(2).upper()
            coords_content = match2.group(3)

            # Parser les coordonnées du modificateur
            coordinates = cls._extract_coordinates_from_content(coords_content)

            coordinate_blocks = cls._parse_coordinate_blocks(command)
            return cls(action, [], coordinates, coordinate_blocks, command)

        # Format alternatif : FLIP HORIZONTAL ([coords])
        pattern_transform = r'^(\w+\s+\w+)\s*\(([^)]+)\)$'
        match_transform = re.match(pattern_transform, command)

        if match_transform:
            action = match_transform.group(1).upper()
            coords_content = match_transform.group(2)

            coordinates = cls._extract_coordinates_from_content(coords_content)
            coordinate_blocks = cls._parse_coordinate_blocks(command)
            return cls(action, [], coordinates, coordinate_blocks, command)

        return None

    @classmethod
    def _parse_standard_command(cls, command: str) -> Optional['UnifiedCommand']:
        """Parse les commandes standards : ACTION paramètres [coordonnées]"""
        # Pattern : ACTION [paramètres] [coordonnées]
        pattern = r'^(\w+(?:\s+\w+)*?)(?:\s+([^\[]*?))?\s*(\[.*)?$'
        match = re.match(pattern, command)

        if not match:
            return None

        action = match.group(1).upper()
        params_str = match.group(2)
        coords_part = match.group(3)

        # Parser les paramètres
        parameters = []
        if params_str and params_str.strip():
            parameters = cls._parse_parameters(params_str.strip(), action)

        # Parser les coordonnées
        coordinates = []
        if coords_part:
            coordinates = cls._extract_coordinates_from_content(coords_part)

        # Parser les blocs de coordonnées pour préserver la sémantique
        coordinate_blocks = cls._parse_coordinate_blocks(command)

        return cls(action, parameters, coordinates, coordinate_blocks, command)

    @classmethod
    def _extract_coordinates_from_content(cls, content: str) -> List[int]:
        """Extrait toutes les coordonnées d'un contenu avec des crochets"""
        coordinates = []
        coord_blocks = re.findall(r'\[([^\]]+)\]', content)
        for block in coord_blocks:
            coordinates.extend(cls._parse_coordinates(block))
        return coordinates

    @classmethod
    def _parse_parameters(cls, params_str: str, action: str) -> List[Any]:
        """Parse les paramètres selon le type de commande moderne"""
        if not params_str:
            return []

        params_str = params_str.strip()

        # INIT avec format "3x3"
        if action == 'INIT' and 'x' in params_str:
            parts = params_str.split('x')
            if len(parts) == 2:
                try:
                    return [int(parts[0]), int(parts[1])]
                except ValueError:
                    pass

        # REPLACE avec couleurs multiples : "1,8,3 5"
        if action == 'REPLACE' and ',' in params_str:
            parts = params_str.split()
            result = []

            for part in parts:
                if ',' in part:
                    # Couleurs sources
                    colors = [int(c.strip()) for c in part.split(',') if c.strip().isdigit()]
                    result.append(colors)
                else:
                    # Couleur cible
                    try:
                        result.append(int(part))
                    except ValueError:
                        result.append(part)
            return result

        # Paramètres standards séparés par espaces
        parts = params_str.split()
        result = []

        for part in parts:
            try:
                result.append(int(part))
            except ValueError:
                result.append(part)

        return result

    @classmethod
    def _parse_coordinate_blocks(cls, raw_command: str) -> List[List[str]]:
        """
        Parse les blocs de coordonnées depuis la commande brute - RESTAURÉ SYSTÈME ORIGINAL
        Retourne un tableau où chaque élément est un bloc de coordonnées :
        - [coord] pour une cellule simple
        - [coord1, coord2] pour un rectangle
        - ['INVERT', coord1, coord2] pour un modificateur INVERT
        - ['COLOR', params, coord1, coord2] pour un modificateur COLOR
        """
        coordinate_blocks = []

        if not raw_command:
            return coordinate_blocks

        # Gestion des opérateurs XOR, AND, OR
        if '(XOR' in raw_command:
            # Format: ACTION (XOR (location1, location2))
            xor_pattern = r'\(XOR\s*\(([^,]+),\s*([^)]+)\)\)'
            match = re.search(xor_pattern, raw_command)
            if match:
                operand1 = match.group(1).strip()
                operand2 = match.group(2).strip()
                # Extraire les coordonnées des deux opérandes
                coords1 = cls._extract_coords_from_operand(operand1)
                coords2 = cls._extract_coords_from_operand(operand2)
                coordinate_blocks.append(['XOR'] + coords1 + ['|'] + coords2)
                return coordinate_blocks

        if '(AND' in raw_command:
            # Format: ACTION (AND (location1, location2, ...))
            and_pattern = r'\(AND\s*\(([^)]+)\)\)'
            match = re.search(and_pattern, raw_command)
            if match:
                operands_str = match.group(1)
                operands = [op.strip() for op in operands_str.split(',')]
                all_coords = ['AND']
                for i, operand in enumerate(operands):
                    if i > 0:
                        all_coords.append('|')  # Séparateur entre opérandes
                    all_coords.extend(cls._extract_coords_from_operand(operand))
                coordinate_blocks.append(all_coords)
                return coordinate_blocks

        if '(OR' in raw_command:
            # Format: ACTION (OR (location1, location2, ...))
            or_pattern = r'\(OR\s*\(([^)]+)\)\)'
            match = re.search(or_pattern, raw_command)
            if match:
                operands_str = match.group(1)
                operands = [op.strip() for op in operands_str.split(',')]
                all_coords = ['OR']
                for i, operand in enumerate(operands):
                    if i > 0:
                        all_coords.append('|')  # Séparateur entre opérandes
                    all_coords.extend(cls._extract_coords_from_operand(operand))
                coordinate_blocks.append(all_coords)
                return coordinate_blocks

        # Gestion des modificateurs INVERT et COLOR
        if '(INVERT' in raw_command:
            # Format: ACTION (INVERT ([coords]))
            invert_pattern = r'\(INVERT\s*\(([^)]+)\)\)'
            match = re.search(invert_pattern, raw_command)
            if match:
                coords_content = match.group(1)
                coord_blocks = re.findall(r'\[([^\]]+)\]', coords_content)
                for block_content in coord_blocks:
                    if ' ' in block_content.strip():
                        # Rectangle avec INVERT
                        coords = block_content.strip().split()
                        coordinate_blocks.append(['INVERT'] + coords)
                    else:
                        # Cellule avec INVERT
                        coordinate_blocks.append(['INVERT', block_content.strip()])
                return coordinate_blocks

        if '(COLOR' in raw_command:
            # Format: ACTION (COLOR params ([coords]))
            color_pattern = r'\(COLOR\s+([^(]+?)\s*\(([^)]+)\)\)'
            match = re.search(color_pattern, raw_command)
            if match:
                color_params = match.group(1).strip()
                coords_content = match.group(2)
                coord_blocks = re.findall(r'\[([^\]]+)\]', coords_content)
                for block_content in coord_blocks:
                    if ' ' in block_content.strip():
                        # Rectangle avec COLOR
                        coords = block_content.strip().split()
                        coordinate_blocks.append(['COLOR', color_params] + coords)
                    else:
                        # Cellule avec COLOR
                        coordinate_blocks.append(['COLOR', color_params, block_content.strip()])
                return coordinate_blocks

        # Extraire tous les blocs [...]
        block_regex = r'\[([^\]]+)\]'
        matches = re.findall(block_regex, raw_command)

        for block_content in matches:
            block_content = block_content.strip()

            if ' ' in block_content:
                # Rectangle: "7,6 13,6" -> ["7,6", "13,6"]
                coords = block_content.split()
                coordinate_blocks.append([coord.strip() for coord in coords if coord.strip()])
            else:
                # Cellule simple: "17,1" -> ["17,1"]
                coordinate_blocks.append([block_content])

        return coordinate_blocks

    @classmethod
    def _parse_coordinates(cls, coords_str: str) -> List[int]:
        """Parse les coordonnées selon le format officiel FONCTIONNEMENT_GENERAL_COMMANDES_UNIFIEES.md"""
        if not coords_str:
            return []

        coords_str = coords_str.strip()
        coordinates = []

        # Format officiel : [ligne,colonne] ou [ligne1,colonne1 ligne2,colonne2]
        if ',' in coords_str:
            if ' ' in coords_str:
                # Zone rectangulaire : [ligne1,colonne1 ligne2,colonne2]
                parts = coords_str.split()
                if len(parts) == 2:
                    try:
                        # Première coordonnée (coin 1)
                        ligne1, colonne1 = parts[0].split(',')
                        coordinates.extend([int(ligne1.strip()), int(colonne1.strip())])

                        # Deuxième coordonnée (coin 2)
                        ligne2, colonne2 = parts[1].split(',')
                        coordinates.extend([int(ligne2.strip()), int(colonne2.strip())])
                    except ValueError:
                        pass
            else:
                # Cellule individuelle : [ligne,colonne]
                try:
                    ligne, colonne = coords_str.split(',')
                    coordinates.extend([int(ligne.strip()), int(colonne.strip())])
                except ValueError:
                    pass

        return coordinates

    @classmethod
    def _extract_coords_from_operand(cls, operand: str) -> List[str]:
        """
        Extrait les coordonnées individuelles d'un opérande pour les opérateurs XOR, AND, OR
        Gère les formats : [1,1], [1,1 3,3], ([1,1] [2,2])
        """
        coords = []
        
        # Nettoyer l'opérande
        clean_operand = operand.strip().replace('(', '').replace(')', '')
        
        # Extraire tous les blocs de coordonnées
        coord_blocks = re.findall(r'\[([^\]]+)\]', clean_operand)
        
        for block in coord_blocks:
            block_content = block.strip()
            if ' ' in block_content:
                # Rectangle : "1,1 3,3" -> ajouter les deux coins
                parts = block_content.split()
                coords.extend(parts)
            else:
                # Cellule simple : "1,1"
                coords.append(block_content)
        
        return coords

    def validate_syntax(self) -> bool:
        """
        Valide la syntaxe de la commande selon les spécifications modernes

        Returns:
            bool: True si la syntaxe est valide
        """
        if not self.action:
            return False

        # Actions modernes supportées + RESIZE pour compatibilité
        modern_actions = {
            'INIT', 'EDIT', 'FILL', 'CLEAR', 'SURROUND', 'REPLACE',
            'INSERT', 'DELETE', 'EXTRACT', 'FLOODFILL',
            'FLIP', 'ROTATE', 'MULTIPLY', 'DIVIDE',
            'COPY', 'CUT', 'PASTE', 'RESIZE', 'END',
            'MOTIF', 'TRANSFERT', 'AND', 'OR', 'XOR'
        }

        if self.action not in modern_actions:
            return False

        # Validations spécifiques modernes
        if self.action == 'INIT':
            return len(self.parameters) == 2 and all(isinstance(p, int) and p > 0 for p in self.parameters)

        elif self.action in ['EDIT', 'FILL']:
            return len(self.parameters) == 1 and len(self.coordinates) >= 2

        elif self.action in ['INSERT', 'DELETE']:
            return len(self.parameters) >= 1

        elif self.action in ['TRANSFERT', 'MOTIF']:
            return len(self.parameters) == 1

        elif self.action in ['FLIP', 'ROTATE']:
            # FLIP et ROTATE n'acceptent plus de coordonnées, opèrent sur le presse-papier
            return len(self.parameters) == 1 and len(self.coordinates) == 0

        elif self.action in ['MULTIPLY', 'DIVIDE']:
            # MULTIPLY et DIVIDE n'acceptent plus de coordonnées, opèrent sur le presse-papier
            return len(self.parameters) >= 1 and len(self.parameters) <= 2 and len(self.coordinates) == 0

        elif self.action == 'END':
            return len(self.parameters) == 0 and len(self.coordinates) == 0

        elif self.action in ['AND', 'OR', 'XOR']:
            # Les opérateurs binaires n'ont pas de paramètres et doivent avoir des coordonnées
            return len(self.parameters) == 0 and len(self.coordinates) >= 4

        # Pour les autres actions modernes, validation basique
        return True

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit la commande en dictionnaire
        
        Returns:
            Dict avec les clés 'action', 'parameters', 'coordinates', 'raw_command'
        """
        return {
            'action': self.action,
            'parameters': self.parameters,
            'coordinates': self.coordinates,
            'raw_command': self.raw_command
        }

    def __str__(self) -> str:
        """Représentation en chaîne de la commande"""
        if self.raw_command:
            return self.raw_command
        
        # Reconstruction de la commande si nécessaire
        parts = [self.action]
        
        if self.parameters:
            parts.extend([str(p) for p in self.parameters])
        
        if self.coordinates:
            # Regrouper les coordonnées par paires pour les rectangles
            if len(self.coordinates) >= 4:
                coords_str = f"[{self.coordinates[0]},{self.coordinates[1]} {self.coordinates[2]},{self.coordinates[3]}]"
            elif len(self.coordinates) >= 2:
                coords_str = f"[{self.coordinates[0]},{self.coordinates[1]}]"
            else:
                coords_str = f"[{self.coordinates[0]}]"
            parts.append(coords_str)
        
        return " ".join(parts)

    def __repr__(self) -> str:
        """Représentation pour debug"""
        return f"UnifiedCommand(action='{self.action}', parameters={self.parameters}, coordinates={self.coordinates})"
