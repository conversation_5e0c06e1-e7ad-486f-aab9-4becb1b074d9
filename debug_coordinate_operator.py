#!/usr/bin/env python3
"""
Debug de l'opérateur de coordonnées AND
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.coordinate_operators import resolve_coordinate_operator

def test_and_operator():
    """Test de l'opérateur AND"""
    
    command = "AND ([0,0 2,2] [0,4 2,6])"
    grid_height = 3
    grid_width = 7
    
    print(f"=== TEST DE L'OPÉRATEUR AND ===")
    print(f"Commande: {command}")
    print(f"Grille: {grid_height}x{grid_width}")
    
    result = resolve_coordinate_operator(command, grid_height, grid_width)
    
    print(f"Coordonnées résolues: {result}")
    print(f"Nombre de coordonnées: {len(result)}")
    
    # Analyser manuellement
    print(f"\n=== ANALYSE MANUELLE ===")
    
    # Zone 1: [0,0 2,2] -> toutes les cellules de (0,0) à (2,2)
    zone1_coords = []
    for r in range(0, 3):  # 0 à 2 inclus
        for c in range(0, 3):  # 0 à 2 inclus
            zone1_coords.append(f"{r},{c}")
    
    print(f"Zone 1 [0,0 2,2]: {zone1_coords}")
    
    # Zone 2: [0,4 2,6] -> toutes les cellules de (0,4) à (2,6)
    zone2_coords = []
    for r in range(0, 3):  # 0 à 2 inclus
        for c in range(4, 7):  # 4 à 6 inclus
            zone2_coords.append(f"{r},{c}")
    
    print(f"Zone 2 [0,4 2,6]: {zone2_coords}")
    
    # Intersection (AND)
    set1 = set(zone1_coords)
    set2 = set(zone2_coords)
    intersection = set1.intersection(set2)
    
    print(f"Intersection (AND): {sorted(list(intersection))}")
    print(f"Résultat attendu: {sorted(list(intersection))}")
    print(f"Résultat obtenu: {result}")
    print(f"Match: {sorted(list(intersection)) == result}")

if __name__ == "__main__":
    test_and_operator()