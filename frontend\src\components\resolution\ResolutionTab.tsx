// src/components/resolution/ResolutionTab.tsx
import React, { useEffect } from 'react';
import { useInitializeGrid, useTaskChangeEffect, useHistoryKeyboardShortcuts, useSelection, useToolStore, useClipboard } from './hooks';
//import { useResolutionStore } from './hooks/useResolutionStore'; // Garder pour resetAll, canUndo/RedoResolution
import { useGridStateStore } from './hooks/useGridStateStore'; // Nouveau store pour l'état de la grille
import { useGridHistoryStore } from './hooks/useGridHistoryStore'; // Importer pour saveGridOnlyHistory
import { useAutomationStore } from './hooks/useAutomationStore';
import { useTask } from '../../hooks/useTask';
import { useProposal } from '../../contexts/ProposalContext';
import { useGrids } from '../../hooks/useGrids';
import { ResolutionGrid } from './ResolutionGrid';
import { SymbolsGroup } from './SymbolsGroup';
import { HistoryGroup } from './HistoryGroup';
import { ActionsGroup } from './ActionsGroup';

import { TransformToolbar, ClipboardToolbar, NewSelectionToolbar, NewOperationsToolbar, NewModificationsToolbar, BinaryOperationsToolbar } from './Toolbar';
import { AutomationPanel } from './automation/AutomationPanel';
import styles from './styles/ResolutionTab.module.css';

/**
 * Composant principal de l'onglet Résolution.
 * Ce composant sera développé progressivement pour inclure toutes les fonctionnalités
 * nécessaires à la résolution des puzzles ARC.
 */
export const ResolutionTab: React.FC = () => {
  // Récupérer l'état de la grille depuis le nouveau store useGridStateStore
  const { getCurrentGrid, setCurrentTestIndex, proposeCurrentGrid: proposeGridFromStateStore } = useGridStateStore();
  const currentGrid = getCurrentGrid();
  
  // L'import de useResolutionStore est nécessaire car le hook useHistoryKeyboardShortcuts (appelé plus bas)
  // dépend de ce store pour canUndoResolution et canRedoResolution.
  // Aucune déstructuration directe n'est faite ici si les valeurs ne sont pas utilisées dans ce composant.
 
  // Récupérer l'état de la sélection
  const { selectedCells/*, enableCommandRecording*/ } = useSelection();

  // Récupérer l'outil actuel
  const { currentTool } = useToolStore();

  // Récupérer l'état du presse-papier pour contrôler l'affichage des transformations
  const { content } = useClipboard();

  // Récupérer la fonction d'initialisation de la grille
  const { initializeGrid } = useInitializeGrid();

  // Récupérer les informations sur la tâche actuelle
  const { currentTask, currentTestPairIndex } = useTask();

  // Récupérer les fonctions de proposition
  const { proposeGridFromEditor, validateProposal } = useProposal();
  const { proposeGrid } = useGrids();
/*
  // Récupérer les fonctions d'automation store
  const { startRecording, isRecording } = useAutomationStore();

  // CORRECTION: Initialiser l'enregistrement automatique au démarrage
  useEffect(() => {
    console.log('[ResolutionTab] Initialisation de l\'enregistrement automatique');
    console.log('[ResolutionTab] État isRecording avant:', isRecording);
    
    // Démarrer l'enregistrement dans les deux stores
    enableCommandRecording();
    startRecording();
    
    console.log('[ResolutionTab] Enregistrement automatique activé');
    console.log('[ResolutionTab] État isRecording après:', useAutomationStore.getState().isRecording);
  }, []); // Une seule fois au montage du composant
*/
  // Ajouter un écouteur d'événement pour gérer l'événement propose-grid
  useEffect(() => {
    const handleProposeGrid = (event: CustomEvent) => {
      const { grid } = event.detail;
      if (grid) {
        proposeGridFromEditor(grid);
        proposeGrid(grid);

      }
    };

    // Ajouter l'écouteur d'événement
    window.addEventListener('propose-grid', handleProposeGrid as EventListener);

    // Nettoyer l'écouteur d'événement lors du démontage
    return () => {
      window.removeEventListener('propose-grid', handleProposeGrid as EventListener);
    };
  }, [proposeGridFromEditor, proposeGrid, currentTestPairIndex]);

  // Ajouter un écouteur d'événement pour gérer l'événement validate-proposal
  useEffect(() => {
    const handleValidateProposal = (event: CustomEvent) => {
      // Récupérer les informations de validation depuis l'événement
      const { success, testIndex, grid } = event.detail || {};

      // Ajouter une commande d'automatisation avec les détails de la grille
      try {
        // Variable automationStore is declared but not used
        // const automationStore = useAutomationStore.getState();

        // Si la grille est disponible, inclure ses détails dans la commande
        if (grid) {
          // Construire les sous-commandes pour la validation
          const subCommands = [
            `RESULT ${success ? 'SUCCESS' : 'FAILURE'}`,
            `TEST ${testIndex}`,
            `GRID ${grid.width}x${grid.height}`
          ];

          // Ajouter des sous-commandes CELL pour chaque cellule non vide
          for (let row = 0; row < grid.height; row++) {
            for (let col = 0; col < grid.width; col++) {
              const value = grid.getCell(row, col);
              if (value !== 0) {
                subCommands.push(`CELL ${row},${col} ${value}`);
              }
            }
          }

          // Joindre toutes les sous-commandes avec des points-virgules
          const validateParams = subCommands.join(';');

          // REFONTE SCENARIOS: "Valider Proposition" n'ajoute plus de commande
          // La validation se fait maintenant uniquement via l'interface sans commande d'automatisation
          console.log('[ResolutionTab] Validation triggered without adding automation command');
        } else {
          // REFONTE SCENARIOS: "Valider Proposition" n'ajoute plus de commande (ancienne version)
          console.log('[ResolutionTab] Validation triggered without adding automation command (legacy)');
        }


      } catch (error) {
        console.error('[ResolutionTab] Failed to add automation command:', error);
      }
    };

    // Fonction pour gérer l'événement validate-from-automation
    const handleValidateFromAutomation = () => {
      // Appeler la fonction validateProposal du contexte de proposition
      try {
        validateProposal();
      } catch (error) {
        console.error('[ResolutionTab] Failed to trigger validation from automation:', error);
      }
    };

    // Ajouter les écouteurs d'événements
    window.addEventListener('validate-proposal', handleValidateProposal as EventListener);
    window.addEventListener('validate-from-automation', handleValidateFromAutomation as EventListener);

    // Nettoyer les écouteurs d'événements lors du démontage
    return () => {
      window.removeEventListener('validate-proposal', handleValidateProposal as EventListener);
      window.removeEventListener('validate-from-automation', handleValidateFromAutomation as EventListener);
    };
  }, [validateProposal]);

  // Utiliser le hook pour surveiller les changements de tâche
  useTaskChangeEffect();

  // Mettre à jour l'index du test actuel dans le store lorsqu'il change
  useEffect(() => {
    setCurrentTestIndex(currentTestPairIndex);
  }, [currentTestPairIndex, setCurrentTestIndex]);

  // Fonction pour proposer la grille actuelle
  const handleProposeClick = () => {


    // Utiliser les fonctions de useGridStateStore pour la proposition
    // et useGridHistoryStore pour la sauvegarde explicite si nécessaire.
    // Si la proposition ne modifie pas la grille, la sauvegarde ici est pour l'action de "proposer".
    // Si `proposeGridFromStateStore` doit sauvegarder, cela devrait être géré en son sein.
    // Pour l'instant, on sauvegarde explicitement l'état avant de proposer.
    const gridStateStore = useGridStateStore.getState();
    useGridHistoryStore.getState().saveGridOnlyHistory(gridStateStore.currentTestIndex, gridStateStore.getCurrentGrid());


    // Récupérer la grille actuelle (déjà disponible via la variable currentGrid en haut du composant)
    // const currentGrid = getCurrentGrid(); // Plus nécessaire ici si currentGrid est à jour
    if (!currentGrid) {
      console.error('[ResolutionTab] Cannot propose: no grid available');
      return;
    }

    // Proposer la grille actuelle via la fonction de useGridStateStore
    proposeGridFromStateStore();

    // Ajouter une commande d'automatisation avec les détails de la grille
    try {
      const automationStore = useAutomationStore.getState();

      // Construire les sous-commandes pour la proposition
      const subCommands = [`GRID ${currentGrid.width}x${currentGrid.height}`];

      // Ajouter des sous-commandes CELL pour chaque cellule non vide
      for (let row = 0; row < currentGrid.height; row++) {
        for (let col = 0; col < currentGrid.width; col++) {
          const value = currentGrid.getCell(row, col);
          if (value !== 0) {
            subCommands.push(`CELL ${row},${col} ${value}`);
          }
        }
      }

      // Joindre toutes les sous-commandes avec des points-virgules
      const proposeParams = subCommands.join(';');

      // REFONTE SCENARIOS: Remplacer PROPOSE par END
      // Ajouter la commande END qui remplace désormais PROPOSE
      automationStore.addUnifiedCommand('END');


    } catch (error) {
      console.error('[ResolutionTab] Failed to add automation command:', error);
    }
  };

  // Initialiser la grille lorsque le composant est monté ou lorsque le test change
  useEffect(() => {
    try {
      if (currentTask && currentTestPairIndex >= 0) {
        // Vérifier si la grille actuelle existe
        if (!getCurrentGrid()) {
          initializeGrid();
          //console.log('[ResolutionTab] Grid initialization triggered due to missing grid', currentTask);
        }
      }
    } catch {
      // Ignorer l'erreur
    }
  }, [currentTask, currentTestPairIndex, getCurrentGrid, initializeGrid]);

  // Utiliser le hook pour les raccourcis clavier
  useHistoryKeyboardShortcuts();


  return (
    <div className={styles.resolutionContainer}>
      {currentGrid ? (
        <div className={styles.mainContainer}>
          <h3 className={styles.tabTitle}>Résolution :</h3>

          {/* Groupes de boutons directement dans le conteneur principal */}
          <div className={styles.toolbarGroups}>
            <ActionsGroup />
            <SymbolsGroup />
            <HistoryGroup />
          </div>

          <div className={styles.gridEditWrapper}>
            {/* Afficher la grille de résolution */}
            <ResolutionGrid />

            {/* Bouton pour proposer la grille */}
            <div className={styles.proposeButtonContainer}>
              <button
                className={styles.proposeButton}
                onClick={handleProposeClick}
                disabled={!currentGrid}
                title="Proposer cette grille comme solution"
              >
                Proposer cette Grille
              </button>
            </div>
          </div>

          {/* Barres d'outils conditionnelles basées sur l'outil actuel */}
          {currentTool === 'select' && (
            <div className={styles.selectionToolbarsHorizontal}>
              {/* ÉTAPE 3 - NOUVELLES BARRES D'OUTILS RÉORGANISÉES */}
              {/* Première ligne : NewSelectionToolbar seule */}
              <NewSelectionToolbar />

              {/* Deuxième ligne : Barres d'outils qui nécessitent une sélection active */}
              <div className={`${styles.toolbarsRow} ${selectedCells.size === 0 ? styles.disabledToolbars : ''}`}>
                {/* CORRECTION ANOMALIE #2 - Barre Clipboard restaurée */}
                <ClipboardToolbar />
                <NewOperationsToolbar />
                <BinaryOperationsToolbar />
                {/* Barre d'outils de transformation - visible uniquement après un Couper ou Coller */}
                {content !== null && <TransformToolbar />}
                <NewModificationsToolbar />
              </div>
            </div>
          )}
          

          {/* Section Automatisation */}
          <AutomationPanel />
        </div>
      ) : (
        <p className={styles.placeholder}>
          Chargez une tâche pour initialiser la grille de résolution.
        </p>
      )}
    </div>
  );
};
