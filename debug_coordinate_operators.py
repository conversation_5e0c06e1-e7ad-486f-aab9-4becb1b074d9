#!/usr/bin/env python3
"""
Debug des opérateurs de coordonnées
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.coordinate_operators import CoordinateOperators

def debug_and_parsing():
    """Debug du parsing de l'opérateur AND"""
    print("=== Debug du parsing AND ===")
    
    command = "AND ([0,0 2,2] [0,4 2,6])"
    print(f"Commande: {command}")
    
    # Test du parsing
    parsed = CoordinateOperators.parse_operator_command(command)
    print(f"Parsed: {parsed}")
    
    # Test de l'exécution
    resolved = CoordinateOperators.execute_operator_command(command, 3, 7)
    print(f"Resolved coords: {resolved}")
    
    # Test manuel des opérandes
    if parsed['valid']:
        operands = parsed['operands']
        print(f"Operands: {operands}")
        
        for i, operand in enumerate(operands):
            coords = CoordinateOperators._extract_coords_from_operand(operand, 3, 7)
            print(f"Operand {i}: {operand} -> {coords}")

if __name__ == "__main__":
    debug_and_parsing()