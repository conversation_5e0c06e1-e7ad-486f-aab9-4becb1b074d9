#!/usr/bin/env python3
"""
Test de la séquence complète des commandes du log
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.command_executor import CommandExecutor
from backend.command_system.unified_command import UnifiedCommand

def test_full_sequence():
    """Test de la séquence complète"""
    print("=== Test de la séquence complète ===")
    
    executor = CommandExecutor()
    
    commands = [
        "TRANSFERT {INIT 7x3; EDIT 1 [0,0]; EDIT 1 [0,2]; EDIT 1 [2,0]; EDIT 1 [2,2]; EDIT 5 [2,3]; EDIT 1 [2,5]}",
        "AND ([0,0 2,2] [0,4 2,6])",
        "REPLACE 1 2 [0,0 2,2]",
        "EXTRACT [0,0 2,2]",
        "END"
    ]
    
    for i, command_str in enumerate(commands):
        print(f"\n--- Commande {i+1}: {command_str} ---")
        
        cmd = UnifiedCommand.parse(command_str)
        if not cmd:
            print(f"❌ Échec du parsing de la commande: {command_str}")
            return False
        
        if not executor._execute_unified_command(cmd):
            print(f"❌ Échec de l'exécution de la commande: {command_str}")
            print(f"Erreur: {executor.error}")
            return False
        
        print("Grille après commande:")
        print(executor.grid)
        print(f"Shape: {executor.grid.shape}")
    
    # Vérifier le résultat final
    expected = [
        [2, 0, 2],
        [0, 0, 0],
        [0, 0, 0]
    ]
    
    print(f"\nRésultat final:")
    print(f"Attendu: {expected}")
    print(f"Obtenu:  {executor.grid.tolist()}")
    
    if executor.grid.tolist() == expected:
        print("✅ Test complet réussi!")
        return True
    else:
        print("❌ Test complet échoué!")
        return False

if __name__ == "__main__":
    test_full_sequence()