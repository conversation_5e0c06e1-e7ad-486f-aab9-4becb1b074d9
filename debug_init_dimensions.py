#!/usr/bin/env python3
"""
Debug des dimensions INIT
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.command_executor import CommandExecutor
from backend.command_system.unified_command import UnifiedCommand

def test_init_dimensions():
    """Test des dimensions INIT"""
    print("=== Test des dimensions INIT ===")
    
    executor = CommandExecutor()
    
    # Test INIT 7x3
    print("Test INIT 7x3:")
    init_cmd = UnifiedCommand.parse("INIT 7x3")
    print(f"Parsed: action={init_cmd.action}, parameters={init_cmd.parameters}")
    
    if executor._execute_unified_command(init_cmd):
        print(f"Grille shape: {executor.grid.shape}")
        print(f"width={executor.width}, height={executor.height}")
        print("Grille:")
        print(executor.grid)
    else:
        print("❌ Échec INIT 7x3")
    
    # Test INIT 3x7
    print("\nTest INIT 3x7:")
    executor2 = CommandExecutor()
    init_cmd2 = UnifiedCommand.parse("INIT 3x7")
    print(f"Parsed: action={init_cmd2.action}, parameters={init_cmd2.parameters}")
    
    if executor2._execute_unified_command(init_cmd2):
        print(f"Grille shape: {executor2.grid.shape}")
        print(f"width={executor2.width}, height={executor2.height}")
        print("Grille:")
        print(executor2.grid)
    else:
        print("❌ Échec INIT 3x7")

if __name__ == "__main__":
    test_init_dimensions()