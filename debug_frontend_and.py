#!/usr/bin/env python3
"""
Test pour simuler exactement ce que fait le frontend avec l'opérateur AND
"""

def extract_coords_from_operand(operand):
    """Simule extractCoordsFromOperand du frontend"""
    coords = []
    
    # Nettoyer l'opérande
    clean_operand = operand.strip()
    
    # Si c'est un rectangle "0,0 2,2"
    if ' ' in clean_operand:
        parts = clean_operand.split()
        if len(parts) == 2:
            try:
                row1, col1 = map(int, parts[0].split(','))
                row2, col2 = map(int, parts[1].split(','))
                
                min_row, max_row = min(row1, row2), max(row1, row2)
                min_col, max_col = min(col1, col2), max(col1, col2)
                
                # Ajouter toutes les cellules du rectangle
                for r in range(min_row, max_row + 1):
                    for c in range(min_col, max_col + 1):
                        coords.append(f"{r},{c}")
            except (ValueError, IndexError):
                pass
    else:
        # Cellule simple "0,0"
        try:
            row, col = map(int, clean_operand.split(','))
            coords.append(f"{row},{col}")
        except (ValueError, IndexError):
            pass
    
    return coords

def resolve_and_operator(operands):
    """Simule resolveAndOperator du frontend"""
    print(f"[resolveAndOperator] Début avec operands: {operands}")
    
    if len(operands) < 2:
        return []
    
    coord_sets = []
    for i, operand in enumerate(operands):
        coords = extract_coords_from_operand(operand)
        print(f"[resolveAndOperator] Operand {i} ({operand}) -> coords: {coords}")
        coord_sets.append(set(coords))
    
    print(f"[resolveAndOperator] coordSets créés: {[list(s) for s in coord_sets]}")
    
    # Commencer avec le premier ensemble
    result = coord_sets[0]
    print(f"[resolveAndOperator] Ensemble initial: {list(result)}")
    
    # Faire l'intersection avec tous les autres ensembles
    for i in range(1, len(coord_sets)):
        intersection = set()
        for coord in result:
            if coord in coord_sets[i]:
                intersection.add(coord)
        print(f"[resolveAndOperator] Intersection avec ensemble {i}: {list(intersection)}")
        result = intersection
    
    result_array = list(result)
    print(f"[AND] Résolution de {len(operands)} opérandes -> {len(result_array)} cellules")
    return result_array

def test_and_operator():
    """Test de l'opérateur AND avec les coordonnées de votre exemple"""
    
    print("=== TEST DE L'OPÉRATEUR AND (SIMULATION FRONTEND) ===")
    
    # Coordonnées de votre exemple
    operands = ["0,0 2,2", "0,4 2,6"]
    
    result = resolve_and_operator(operands)
    
    print(f"\n=== RÉSULTAT ===")
    print(f"Opérandes: {operands}")
    print(f"Intersection: {result}")
    print(f"Intersection vide: {len(result) == 0}")
    
    if len(result) == 0:
        print("✅ L'intersection est vide, donc l'opérateur AND ne devrait rien faire")
    else:
        print(f"❌ L'intersection contient {len(result)} cellules: {result}")

if __name__ == "__main__":
    test_and_operator()