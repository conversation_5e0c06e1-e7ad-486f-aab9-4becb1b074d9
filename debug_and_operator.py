#!/usr/bin/env python3
"""
Debug spécifique de l'opérateur AND
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.command_system.coordinate_operators import CoordinateOperators

def debug_and_operator():
    """Debug de l'opérateur AND"""
    print("=== Debug de l'opérateur AND ===")
    
    operands = ['[0,0 2,2]', '[0,4 2,6]']
    print(f"Operands: {operands}")
    
    # Test de resolve_and_operator
    result = CoordinateOperators.resolve_and_operator(operands, 3, 7)
    print(f"AND result: {result}")
    
    # Debug étape par étape
    coord_sets = []
    for operand in operands:
        coords = CoordinateOperators._extract_coords_from_operand(operand, 3, 7)
        coord_set = set(coords)
        coord_sets.append(coord_set)
        print(f"Operand {operand} -> coords: {coords}")
        print(f"Operand {operand} -> set: {coord_set}")
    
    # Intersection
    result_set = coord_sets[0]
    print(f"Initial set: {result_set}")
    
    for i, coord_set in enumerate(coord_sets[1:], 1):
        print(f"Intersecting with set {i}: {coord_set}")
        result_set = result_set.intersection(coord_set)
        print(f"Result after intersection {i}: {result_set}")
    
    final_result = sorted(list(result_set))
    print(f"Final result: {final_result}")

if __name__ == "__main__":
    debug_and_operator()